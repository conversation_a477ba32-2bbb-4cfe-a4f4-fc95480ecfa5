# Dr<PERSON> <PERSON><PERSON> Neurosurgery Website - Production Environment
# IMPORTANT: Replace placeholder values with actual production values

# Application Environment (REQUIRED)
VITE_APP_ENV=production
VITE_APP_NAME="miNEURO Brain and Spine Surgery"
VITE_APP_VERSION=1.0.0

# Website Configuration (REQUIRED)
VITE_SITE_URL=https://mineuro.com.au
VITE_API_BASE_URL=https://api.mineuro.com.au
VITE_CDN_URL=https://cdn.mineuro.com.au

# Contact Information (REQUIRED)
VITE_PRACTICE_PHONE="+61 3 9008 4200"
VITE_PRACTICE_FAX="+61 3 9923 6688"
VITE_PRACTICE_EMAIL="<EMAIL>"
VITE_ARGUS_EMAIL="<EMAIL>"
VITE_HEALTHLINK_ID="mineuros"

# Social Media (Optional)
VITE_FACEBOOK_URL="https://www.facebook.com/mineuro"
VITE_LINKEDIN_URL="https://www.linkedin.com/company/mineuro"
VITE_TWITTER_HANDLE="@mineuro"

# Analytics and Tracking (Recommended for Production)
# IMPORTANT: Replace with actual values before deployment
VITE_GOOGLE_ANALYTICS_ID=
VITE_GOOGLE_TAG_MANAGER_ID=
VITE_HOTJAR_ID=
VITE_FACEBOOK_PIXEL_ID=

# Maps and Location Services (Recommended)
# IMPORTANT: Replace with actual API key before deployment
VITE_GOOGLE_MAPS_API_KEY=
VITE_MAPBOX_ACCESS_TOKEN=

# Email Services (Required for Contact Forms)
# IMPORTANT: Replace with actual EmailJS credentials before deployment
VITE_EMAILJS_SERVICE_ID=
VITE_EMAILJS_TEMPLATE_ID=
VITE_EMAILJS_PUBLIC_KEY=

# Error Reporting (Recommended for Production)
# IMPORTANT: Replace with actual Sentry DSN before deployment
VITE_SENTRY_DSN=
VITE_SENTRY_ENVIRONMENT=production
VITE_ERROR_REPORTING_ENDPOINT=
VITE_ERROR_REPORTING_API_KEY=

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_BOUNDARY=true

# Feature Flags
VITE_ENABLE_DARK_MODE=false
VITE_ENABLE_LANGUAGE_SWITCHING=true
VITE_ENABLE_APPOINTMENT_BOOKING=true
VITE_ENABLE_LIVE_CHAT=true

# Security Configuration (Production)
VITE_ENABLE_CSP=true
VITE_ENABLE_HSTS=true

# Development Only (MUST be false in production)
VITE_DEBUG_MODE=false
VITE_SHOW_PERFORMANCE_METRICS=false

# Production Optimization Flags
VITE_NODE_ENV=production
VITE_MINIFY=true
VITE_SOURCEMAP=false

# Production Security Flags
VITE_DISABLE_CONSOLE=true
VITE_DISABLE_DEVTOOLS=true
VITE_STRICT_MODE=true
