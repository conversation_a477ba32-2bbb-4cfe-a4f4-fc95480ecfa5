{"timestamp": "2025-07-21T05:03:55.416Z", "summary": {"totalFiles": 456, "compliantFiles": 338, "violatingFiles": 118, "worstViolation": {"file": "src\\pages\\patient-resources\\conditions\\CervicalMyelopathy.tsx", "lines": 772, "type": "feature", "limit": 250, "violation": true, "excess": 522}}, "violations": [{"file": "src\\pages\\patient-resources\\conditions\\CervicalMyelopathy.tsx", "lines": 772, "type": "feature", "limit": 250, "violation": true, "excess": 522}, {"file": "src\\pages\\patient-resources\\BrainConditions.tsx", "lines": 701, "type": "feature", "limit": 250, "violation": true, "excess": 451}, {"file": "src\\pages\\patient-resources\\conditions\\Hydrocephalus.tsx", "lines": 680, "type": "feature", "limit": 250, "violation": true, "excess": 430}, {"file": "src\\pages\\patient-resources\\PeripheralNerveConditions.tsx", "lines": 653, "type": "feature", "limit": 250, "violation": true, "excess": 403}, {"file": "src\\pages\\patient-resources\\conditions\\ChiariMalformation.tsx", "lines": 644, "type": "feature", "limit": 250, "violation": true, "excess": 394}, {"file": "src\\pages\\patient-resources\\ExerciseLibrary.tsx", "lines": 631, "type": "feature", "limit": 250, "violation": true, "excess": 381}, {"file": "src\\pages\\patient-resources\\conditions\\CaudaEquinaSyndrome.tsx", "lines": 621, "type": "feature", "limit": 250, "violation": true, "excess": 371}, {"file": "src\\pages\\patient-resources\\conditions\\CerebralAVM.tsx", "lines": 614, "type": "feature", "limit": 250, "violation": true, "excess": 364}, {"file": "src\\pages\\patient-resources\\CervicalSpineExercises.tsx", "lines": 541, "type": "feature", "limit": 250, "violation": true, "excess": 291}, {"file": "src\\pages\\patient-resources\\SpineSafeExercises.tsx", "lines": 538, "type": "feature", "limit": 250, "violation": true, "excess": 288}, {"file": "src\\pages\\patient-resources\\conditions\\CerebralMeningioma.tsx", "lines": 501, "type": "feature", "limit": 250, "violation": true, "excess": 251}, {"file": "src\\components\\medical-conditions\\peripheral-nerve-tumors\\NerveTumorExerciseSection.tsx", "lines": 500, "type": "feature", "limit": 250, "violation": true, "excess": 250}, {"file": "src\\pages\\patient-resources\\YouthfulSpine.tsx", "lines": 498, "type": "feature", "limit": 250, "violation": true, "excess": 248}, {"file": "src\\pages\\patient-resources\\conditions\\CerebralCavernoma.tsx", "lines": 497, "type": "feature", "limit": 250, "violation": true, "excess": 247}, {"file": "src\\components\\medical-conditions\\peroneal-nerve-palsy\\PeronealExerciseSection.tsx", "lines": 494, "type": "feature", "limit": 250, "violation": true, "excess": 244}, {"file": "src\\components\\medical-conditions\\cervical-myelopathy\\MyelopathySymptomAssessment.tsx", "lines": 493, "type": "feature", "limit": 250, "violation": true, "excess": 243}, {"file": "src\\components\\medical-conditions\\meralgia-paresthetica\\MeralgiaExerciseSection.tsx", "lines": 491, "type": "feature", "limit": 250, "violation": true, "excess": 241}, {"file": "src\\pages\\patient-resources\\SpineAnatomy.tsx", "lines": 487, "type": "feature", "limit": 250, "violation": true, "excess": 237}, {"file": "src\\pages\\patient-resources\\conditions\\CerebralAneurysm.tsx", "lines": 484, "type": "feature", "limit": 250, "violation": true, "excess": 234}, {"file": "src\\pages\\patient-resources\\PatientDashboard.tsx", "lines": 482, "type": "feature", "limit": 250, "violation": true, "excess": 232}, {"file": "src\\components\\medical-conditions\\peripheral-nerve-tumors\\NerveTumorTreatmentComparison.tsx", "lines": 474, "type": "feature", "limit": 250, "violation": true, "excess": 224}, {"file": "src\\components\\medical-conditions\\peroneal-nerve-palsy\\PeronealTreatmentComparison.tsx", "lines": 472, "type": "feature", "limit": 250, "violation": true, "excess": 222}, {"file": "src\\components\\medical-conditions\\tarsal-tunnel-syndrome\\TarsalTreatmentComparison.tsx", "lines": 471, "type": "feature", "limit": 250, "violation": true, "excess": 221}, {"file": "src\\components\\medical-conditions\\tarsal-tunnel-syndrome\\TarsalErgonomicsSection.tsx", "lines": 470, "type": "feature", "limit": 250, "violation": true, "excess": 220}, {"file": "src\\pages\\gp-resources\\Diagnostics.tsx", "lines": 454, "type": "feature", "limit": 250, "violation": true, "excess": 204}, {"file": "src\\pages\\patient-resources\\AssessmentTools.tsx", "lines": 453, "type": "feature", "limit": 250, "violation": true, "excess": 203}, {"file": "src\\components\\medical-conditions\\peripheral-nerve-tumors\\NerveTumorWarningSignsSection.tsx", "lines": 451, "type": "feature", "limit": 250, "violation": true, "excess": 201}, {"file": "src\\components\\medical-conditions\\meralgia-paresthetica\\MeralgiaComparisonSection.tsx", "lines": 447, "type": "feature", "limit": 250, "violation": true, "excess": 197}, {"file": "src\\components\\medical-conditions\\peroneal-nerve-palsy\\PeronealErgonomicsSection.tsx", "lines": 445, "type": "feature", "limit": 250, "violation": true, "excess": 195}, {"file": "src\\components\\medical-conditions\\meralgia-paresthetica\\MeralgiaErgonomicsSection.tsx", "lines": 444, "type": "feature", "limit": 250, "violation": true, "excess": 194}, {"file": "src\\components\\medical-conditions\\tarsal-tunnel-syndrome\\TarsalExerciseSection.tsx", "lines": 444, "type": "feature", "limit": 250, "violation": true, "excess": 194}, {"file": "src\\components\\medical-conditions\\hydrocephalus\\HydrocephalusSymptomAssessment.tsx", "lines": 439, "type": "feature", "limit": 250, "violation": true, "excess": 189}, {"file": "src\\pages\\expertise\\RoboticSpineSurgery.tsx", "lines": 436, "type": "feature", "limit": 250, "violation": true, "excess": 186}, {"file": "src\\components\\medical-conditions\\peroneal-nerve-palsy\\PeronealWarningSignsSection.tsx", "lines": 430, "type": "feature", "limit": 250, "violation": true, "excess": 180}, {"file": "src\\components\\medical-conditions\\tarsal-tunnel-syndrome\\TarsalWarningSignsSection.tsx", "lines": 430, "type": "feature", "limit": 250, "violation": true, "excess": 180}, {"file": "src\\components\\medical-conditions\\cauda-equina-syndrome\\EmergencyAssessmentTool.tsx", "lines": 425, "type": "feature", "limit": 250, "violation": true, "excess": 175}, {"file": "src\\components\\medical-conditions\\hemifacial-spasm\\HemifacialSymptomAssessment.tsx", "lines": 424, "type": "feature", "limit": 250, "violation": true, "excess": 174}, {"file": "src\\pages\\gp-resources\\CareCoordination.tsx", "lines": 422, "type": "feature", "limit": 250, "violation": true, "excess": 172}, {"file": "src\\components\\medical-conditions\\peripheral-nerve-tumors\\NerveTumorErgonomicsSection.tsx", "lines": 418, "type": "feature", "limit": 250, "violation": true, "excess": 168}, {"file": "src\\pages\\patient-resources\\CervicalSpineInjury.tsx", "lines": 417, "type": "feature", "limit": 250, "violation": true, "excess": 167}, {"file": "src\\components\\medical-conditions\\meralgia-paresthetica\\MeralgiaWarningSignsSection.tsx", "lines": 416, "type": "feature", "limit": 250, "violation": true, "excess": 166}, {"file": "src\\components\\medical-conditions\\cervical-myelopathy\\SurgicalTreatmentComparison.tsx", "lines": 412, "type": "feature", "limit": 250, "violation": true, "excess": 162}, {"file": "src\\pages\\PatientResources.tsx", "lines": 410, "type": "feature", "limit": 250, "violation": true, "excess": 160}, {"file": "src\\pages\\patient-resources\\ExercisePainMedRisks.tsx", "lines": 409, "type": "feature", "limit": 250, "violation": true, "excess": 159}, {"file": "src\\components\\medical-conditions\\cerebral-aneurysm\\RiskAssessmentTool.tsx", "lines": 398, "type": "feature", "limit": 250, "violation": true, "excess": 148}, {"file": "src\\components\\medical-conditions\\brain-tumour\\JourneyTracker.tsx", "lines": 395, "type": "feature", "limit": 250, "violation": true, "excess": 145}, {"file": "src\\components\\medical-conditions\\cerebral-meningioma\\MeningiomaSymptomAssessment.tsx", "lines": 389, "type": "feature", "limit": 250, "violation": true, "excess": 139}, {"file": "src\\components\\medical-conditions\\chiari-malformation\\ChiariSymptomAssessment.tsx", "lines": 389, "type": "feature", "limit": 250, "violation": true, "excess": 139}, {"file": "src\\components\\medical-conditions\\cerebral-avm\\AVMSymptomAssessment.tsx", "lines": 386, "type": "feature", "limit": 250, "violation": true, "excess": 136}, {"file": "src\\components\\medical-conditions\\cerebral-cavernoma\\CavernomaSymptomAssessment.tsx", "lines": 386, "type": "feature", "limit": 250, "violation": true, "excess": 136}, {"file": "src\\components\\medical-conditions\\hydrocephalus\\ShuntSystemsComparison.tsx", "lines": 384, "type": "feature", "limit": 250, "violation": true, "excess": 134}, {"file": "src\\pages\\patient-resources\\ConditionInformation.tsx", "lines": 381, "type": "feature", "limit": 250, "violation": true, "excess": 131}, {"file": "src\\components\\medical-conditions\\hemifacial-spasm\\HemifacialTreatmentComparison.tsx", "lines": 378, "type": "feature", "limit": 250, "violation": true, "excess": 128}, {"file": "src\\pages\\expertise\\ImageGuidedSurgery.tsx", "lines": 377, "type": "feature", "limit": 250, "violation": true, "excess": 127}, {"file": "src\\components\\medical-conditions\\peripheral-nerve-tumors\\NerveTumorAnatomySection.tsx", "lines": 375, "type": "feature", "limit": 250, "violation": true, "excess": 125}, {"file": "src\\pages\\locations\\LocationDetail.tsx", "lines": 374, "type": "feature", "limit": 250, "violation": true, "excess": 124}, {"file": "src\\components\\medical-conditions\\chiari-malformation\\ChiariTreatmentComparison.tsx", "lines": 373, "type": "feature", "limit": 250, "violation": true, "excess": 123}, {"file": "src\\components\\medical-conditions\\hemifacial-spasm\\SpasmProgressionSection.tsx", "lines": 372, "type": "feature", "limit": 250, "violation": true, "excess": 122}, {"file": "src\\components\\medical-conditions\\cerebral-aneurysm\\TreatmentComparison.tsx", "lines": 368, "type": "feature", "limit": 250, "violation": true, "excess": 118}, {"file": "src\\components\\medical-conditions\\cerebral-meningioma\\MeningiomaTreatmentComparison.tsx", "lines": 368, "type": "feature", "limit": 250, "violation": true, "excess": 118}, {"file": "src\\pages\\patient-resources\\SpineConditionsLibrary.tsx", "lines": 367, "type": "feature", "limit": 250, "violation": true, "excess": 117}, {"file": "src\\components\\medical-conditions\\brain-tumour\\TreatmentDecisionHelper.tsx", "lines": 365, "type": "feature", "limit": 250, "violation": true, "excess": 115}, {"file": "src\\components\\medical-conditions\\cerebral-avm\\AVMTreatmentComparison.tsx", "lines": 363, "type": "feature", "limit": 250, "violation": true, "excess": 113}, {"file": "src\\components\\medical-conditions\\trigeminal-neuralgia\\SurgicalOptionsComparison.tsx", "lines": 363, "type": "feature", "limit": 250, "violation": true, "excess": 113}, {"file": "src\\pages\\patient-resources\\conditions\\TrigeminalNeuralgia.tsx", "lines": 358, "type": "feature", "limit": 250, "violation": true, "excess": 108}, {"file": "src\\components\\medical-conditions\\peroneal-nerve-palsy\\PeronealAnatomySection.tsx", "lines": 356, "type": "feature", "limit": 250, "violation": true, "excess": 106}, {"file": "src\\components\\medical-conditions\\tarsal-tunnel-syndrome\\TarsalAnatomySection.tsx", "lines": 356, "type": "feature", "limit": 250, "violation": true, "excess": 106}, {"file": "src\\components\\medical-conditions\\cerebral-cavernoma\\CavernomaTreatmentComparison.tsx", "lines": 354, "type": "feature", "limit": 250, "violation": true, "excess": 104}, {"file": "src\\components\\medical-conditions\\trigeminal-neuralgia\\PainAssessmentTool.tsx", "lines": 354, "type": "feature", "limit": 250, "violation": true, "excess": 104}, {"file": "src\\pages\\Expertise.tsx", "lines": 348, "type": "feature", "limit": 250, "violation": true, "excess": 98}, {"file": "src\\pages\\patient-resources\\SpineHealthApp.tsx", "lines": 346, "type": "feature", "limit": 250, "violation": true, "excess": 96}, {"file": "src\\components\\medical-conditions\\cauda-equina-syndrome\\CESEmergencyProtocol.tsx", "lines": 344, "type": "feature", "limit": 250, "violation": true, "excess": 94}, {"file": "src\\components\\medical-conditions\\cerebral-aneurysm\\WarningSignsSection.tsx", "lines": 340, "type": "feature", "limit": 250, "violation": true, "excess": 90}, {"file": "src\\components\\EmptyState.tsx", "lines": 337, "type": "feature", "limit": 250, "violation": true, "excess": 87}, {"file": "src\\pages\\gp-resources\\Emergencies.tsx", "lines": 334, "type": "feature", "limit": 250, "violation": true, "excess": 84}, {"file": "src\\components\\medical-conditions\\trigeminal-neuralgia\\PainTriggerManagement.tsx", "lines": 334, "type": "feature", "limit": 250, "violation": true, "excess": 84}, {"file": "src\\components\\medical-conditions\\cervical-myelopathy\\SpinalAnatomySection.tsx", "lines": 329, "type": "feature", "limit": 250, "violation": true, "excess": 79}, {"file": "src\\components\\shared\\CommonSectionPatterns.tsx", "lines": 326, "type": "feature", "limit": 250, "violation": true, "excess": 76}, {"file": "src\\components\\medical-conditions\\cauda-equina-syndrome\\RedFlagsWarningSection.tsx", "lines": 323, "type": "feature", "limit": 250, "violation": true, "excess": 73}, {"file": "src\\components\\medical-conditions\\cerebral-cavernoma\\CavernomaLocationsSection.tsx", "lines": 321, "type": "feature", "limit": 250, "violation": true, "excess": 71}, {"file": "src\\components\\medical-conditions\\cerebral-meningioma\\MeningiomaLocationsSection.tsx", "lines": 318, "type": "feature", "limit": 250, "violation": true, "excess": 68}, {"file": "src\\pages\\patient-resources\\conditions\\FacetArthropathy.tsx", "lines": 311, "type": "feature", "limit": 250, "violation": true, "excess": 61}, {"file": "src\\pages\\patient-resources\\IndividualSpineHealthProgramme.tsx", "lines": 301, "type": "feature", "limit": 250, "violation": true, "excess": 51}, {"file": "src\\pages\\gp-resources\\ReferralProtocols.tsx", "lines": 299, "type": "feature", "limit": 250, "violation": true, "excess": 49}, {"file": "src\\pages\\Locations.tsx", "lines": 299, "type": "feature", "limit": 250, "violation": true, "excess": 49}, {"file": "src\\components\\shared\\CommonLayoutPatterns.tsx", "lines": 298, "type": "feature", "limit": 250, "violation": true, "excess": 48}, {"file": "src\\pages\\expertise\\lumbar-disc-replacement\\RisksComparison.tsx", "lines": 295, "type": "feature", "limit": 250, "violation": true, "excess": 45}, {"file": "src\\components\\AsyncContent.tsx", "lines": 295, "type": "feature", "limit": 250, "violation": true, "excess": 45}, {"file": "src\\components\\medical-conditions\\hydrocephalus\\CSFAnatomySection.tsx", "lines": 292, "type": "feature", "limit": 250, "violation": true, "excess": 42}, {"file": "src\\pages\\patient-resources\\SpineAndBrainHealth.tsx", "lines": 291, "type": "feature", "limit": 250, "violation": true, "excess": 41}, {"file": "src\\components\\medical-conditions\\hemifacial-spasm\\FacialAnatomySection.tsx", "lines": 290, "type": "feature", "limit": 250, "violation": true, "excess": 40}, {"file": "src\\components\\medical-conditions\\shared\\ErgonomicGuidanceSection.tsx", "lines": 289, "type": "feature", "limit": 250, "violation": true, "excess": 39}, {"file": "src\\components\\medical-conditions\\cerebral-avm\\AVMGradingSystem.tsx", "lines": 279, "type": "feature", "limit": 250, "violation": true, "excess": 29}, {"file": "src\\components\\medical-conditions\\ulnar-neuropathy\\UlnarExerciseGuide.tsx", "lines": 276, "type": "feature", "limit": 250, "violation": true, "excess": 26}, {"file": "src\\components\\shared\\TreatmentComparisonBase.tsx", "lines": 274, "type": "feature", "limit": 250, "violation": true, "excess": 24}, {"file": "src\\components\\contact\\InteractiveMapsSection.tsx", "lines": 271, "type": "feature", "limit": 250, "violation": true, "excess": 21}, {"file": "src\\components\\medical-conditions\\cerebral-aneurysm\\AneurysmAnatomySection.tsx", "lines": 270, "type": "feature", "limit": 250, "violation": true, "excess": 20}, {"file": "src\\components\\medical-conditions\\cerebral-meningioma\\MeningiomaAnatomySection.tsx", "lines": 270, "type": "feature", "limit": 250, "violation": true, "excess": 20}, {"file": "src\\components\\medical-conditions\\cauda-equina-syndrome\\SpinalAnatomySection.tsx", "lines": 269, "type": "feature", "limit": 250, "violation": true, "excess": 19}, {"file": "src\\components\\medical-conditions\\cerebral-avm\\AVMAnatomySection.tsx", "lines": 269, "type": "feature", "limit": 250, "violation": true, "excess": 19}, {"file": "src\\components\\medical-conditions\\cerebral-cavernoma\\CavernomaAnatomySection.tsx", "lines": 269, "type": "feature", "limit": 250, "violation": true, "excess": 19}, {"file": "src\\components\\medical-conditions\\chiari-malformation\\BrainAnatomySection.tsx", "lines": 269, "type": "feature", "limit": 250, "violation": true, "excess": 19}, {"file": "src\\components\\medical-conditions\\meralgia-paresthetica\\MeralgiaAnatomySection.tsx", "lines": 267, "type": "feature", "limit": 250, "violation": true, "excess": 17}, {"file": "src\\pages\\expertise\\cervical-disc-replacement\\RisksComparison.tsx", "lines": 265, "type": "feature", "limit": 250, "violation": true, "excess": 15}, {"file": "src\\components\\contact\\HospitalAffiliationsSection.tsx", "lines": 265, "type": "feature", "limit": 250, "violation": true, "excess": 15}, {"file": "src\\pages\\expertise\\lumbar-disc-replacement\\SurgeryRecovery.tsx", "lines": 263, "type": "feature", "limit": 250, "violation": true, "excess": 13}, {"file": "src\\components\\medical-conditions\\ulnar-neuropathy\\UlnarWarningSignsSection.tsx", "lines": 262, "type": "feature", "limit": 250, "violation": true, "excess": 12}, {"file": "src\\components\\medical-conditions\\shared\\ExerciseGuideSection.tsx", "lines": 259, "type": "feature", "limit": 250, "violation": true, "excess": 9}, {"file": "src\\hooks\\useContentState.ts", "lines": 259, "type": "feature", "limit": 250, "violation": true, "excess": 9}, {"file": "src\\components\\medical-conditions\\brain-tumour\\SymptomAssessment.tsx", "lines": 257, "type": "feature", "limit": 250, "violation": true, "excess": 7}, {"file": "src\\components\\locations\\GenericLocationTemplate.tsx", "lines": 256, "type": "feature", "limit": 250, "violation": true, "excess": 6}, {"file": "src\\components\\medical-conditions\\facet-arthropathy\\FacetJointAnatomySection.tsx", "lines": 255, "type": "feature", "limit": 250, "violation": true, "excess": 5}, {"file": "src\\components\\medical-conditions\\shared\\ConditionTreatment.tsx", "lines": 254, "type": "feature", "limit": 250, "violation": true, "excess": 4}, {"file": "src\\components\\medical-conditions\\ulnar-neuropathy\\UlnarErgonomicGuide.tsx", "lines": 253, "type": "feature", "limit": 250, "violation": true, "excess": 3}, {"file": "src\\components\\medical-conditions\\trigeminal-neuralgia\\MedicationComparison.tsx", "lines": 252, "type": "feature", "limit": 250, "violation": true, "excess": 2}, {"file": "src\\pages\\Gallery.tsx", "lines": 251, "type": "feature", "limit": 250, "violation": true, "excess": 1}, {"file": "src\\components\\medical-conditions\\arthrosis\\ArthrosisTypesSection.tsx", "lines": 251, "type": "feature", "limit": 250, "violation": true, "excess": 1}, {"file": "src\\components\\medical-conditions\\shared\\WarningSignsSection.tsx", "lines": 251, "type": "feature", "limit": 250, "violation": true, "excess": 1}], "limits": {"page": 300, "feature": 250, "ui": 200, "utility": 150}}