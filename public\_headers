/*
  # Security Headers
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

  # Content Security Policy - Production Ready
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://static.hotjar.com https://script.hotjar.com https://maps.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://maps.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob: https://maps.googleapis.com https://maps.gstatic.com; media-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com https://region1.google-analytics.com https://stats.g.doubleclick.net https://in.hotjar.com https://vc.hotjar.io wss://ws.hotjar.com https://maps.googleapis.com https://api.mineuro.com.au wss://localhost:* ws://localhost:*; frame-src 'self' https://www.google.com https://vars.hotjar.com https://maps.google.com; object-src 'none'; base-uri 'self'; form-action 'self' https://api.mineuro.com.au; frame-ancestors 'none'; upgrade-insecure-requests; report-uri /csp-report

  # Permissions Policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(self), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()

  # Strict Transport Security (HTTPS only)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

  # Cross-Origin Policies
  Cross-Origin-Embedder-Policy: credentialless
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin

  # Additional Security Headers
  X-Permitted-Cross-Domain-Policies: none
  X-Download-Options: noopen
  Expect-CT: max-age=86400, enforce

  # Cache Control
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

/*.png
  Content-Type: image/png
  Cache-Control: public, max-age=31536000, immutable

/*.jpg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=31536000, immutable

/*.jpeg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=31536000, immutable

/*.webp
  Content-Type: image/webp
  Cache-Control: public, max-age=31536000, immutable

/*.svg
  Content-Type: image/svg+xml
  Cache-Control: public, max-age=31536000, immutable

/*.ico
  Content-Type: image/x-icon
  Cache-Control: public, max-age=31536000, immutable

/manifest.json
  Content-Type: application/manifest+json

/sw.js
  Content-Type: application/javascript; charset=utf-8
