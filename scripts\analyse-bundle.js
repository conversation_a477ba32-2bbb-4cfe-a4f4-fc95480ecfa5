#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * Analyzes the production build for optimization opportunities
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(process.cwd(), 'dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  console.log(`${colors.cyan}${colors.bright}📦 BUNDLE ANALYSIS REPORT${colors.reset}`);
  console.log('==================================================\n');

  if (!fs.existsSync(DIST_DIR)) {
    console.log(`${colors.red}❌ Build directory not found. Please run 'npm run build' first.${colors.reset}`);
    process.exit(1);
  }

  // Analyze assets
  const assets = [];
  let totalSize = 0;
  let totalGzipSize = 0;

  if (fs.existsSync(ASSETS_DIR)) {
    const files = fs.readdirSync(ASSETS_DIR);
    
    files.forEach(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        const size = stats.size;
        totalSize += size;
        
        // Try to get gzip size (approximate)
        let gzipSize = size * 0.3; // Rough estimate
        try {
          // If gzip is available, get actual compressed size
          const compressed = execSync(`gzip -c "${filePath}" | wc -c`, { encoding: 'utf8' });
          gzipSize = parseInt(compressed.trim());
        } catch (e) {
          // Fallback to estimate
        }
        totalGzipSize += gzipSize;
        
        assets.push({
          name: file,
          size,
          gzipSize,
          type: getFileType(file)
        });
      }
    });
  }

  // Sort by size (largest first)
  assets.sort((a, b) => b.size - a.size);

  console.log(`${colors.blue}📊 Bundle Size Summary:${colors.reset}`);
  console.log(`   Total Size: ${colors.bright}${formatBytes(totalSize)}${colors.reset}`);
  console.log(`   Gzipped: ${colors.bright}${formatBytes(totalGzipSize)}${colors.reset}`);
  console.log(`   Compression Ratio: ${colors.bright}${((1 - totalGzipSize / totalSize) * 100).toFixed(1)}%${colors.reset}\n`);

  // Analyze by file type
  const typeAnalysis = {};
  assets.forEach(asset => {
    if (!typeAnalysis[asset.type]) {
      typeAnalysis[asset.type] = { count: 0, size: 0, gzipSize: 0 };
    }
    typeAnalysis[asset.type].count++;
    typeAnalysis[asset.type].size += asset.size;
    typeAnalysis[asset.type].gzipSize += asset.gzipSize;
  });

  console.log(`${colors.blue}📁 Assets by Type:${colors.reset}`);
  Object.entries(typeAnalysis).forEach(([type, data]) => {
    console.log(`   ${type}: ${data.count} files, ${formatBytes(data.size)} (${formatBytes(data.gzipSize)} gzipped)`);
  });
  console.log();

  // Show largest files
  console.log(`${colors.blue}📋 Largest Assets:${colors.reset}`);
  assets.slice(0, 10).forEach((asset, index) => {
    const sizeColor = asset.size > 100000 ? colors.red : asset.size > 50000 ? colors.yellow : colors.green;
    console.log(`   ${index + 1}. ${asset.name}`);
    console.log(`      Size: ${sizeColor}${formatBytes(asset.size)}${colors.reset} (${formatBytes(asset.gzipSize)} gzipped)`);
  });
  console.log();

  // Performance recommendations
  console.log(`${colors.magenta}💡 Performance Recommendations:${colors.reset}`);
  
  const largeAssets = assets.filter(asset => asset.size > 100000);
  if (largeAssets.length > 0) {
    console.log(`   ${colors.yellow}⚠️  Large assets detected (>100KB):${colors.reset}`);
    largeAssets.forEach(asset => {
      console.log(`      • ${asset.name} (${formatBytes(asset.size)})`);
    });
    console.log(`      Consider code splitting or lazy loading for these assets.\n`);
  }

  const jsAssets = assets.filter(asset => asset.type === 'JavaScript');
  const totalJsSize = jsAssets.reduce((sum, asset) => sum + asset.size, 0);
  if (totalJsSize > 500000) {
    console.log(`   ${colors.yellow}⚠️  Total JavaScript size is large (${formatBytes(totalJsSize)})${colors.reset}`);
    console.log(`      Consider implementing more aggressive code splitting.\n`);
  }

  const cssAssets = assets.filter(asset => asset.type === 'CSS');
  const totalCssSize = cssAssets.reduce((sum, asset) => sum + asset.size, 0);
  if (totalCssSize > 200000) {
    console.log(`   ${colors.yellow}⚠️  Total CSS size is large (${formatBytes(totalCssSize)})${colors.reset}`);
    console.log(`      Consider CSS purging or critical CSS extraction.\n`);
  }

  // Check for potential issues
  const vendorAssets = assets.filter(asset => asset.name.includes('vendor'));
  if (vendorAssets.length > 5) {
    console.log(`   ${colors.yellow}⚠️  Many vendor chunks detected (${vendorAssets.length})${colors.reset}`);
    console.log(`      Consider optimizing vendor chunk splitting.\n`);
  }

  console.log(`${colors.green}✅ Bundle analysis complete!${colors.reset}`);
  console.log('==================================================');
}

function getFileType(filename) {
  const ext = path.extname(filename).toLowerCase();
  switch (ext) {
    case '.js':
      return 'JavaScript';
    case '.css':
      return 'CSS';
    case '.html':
      return 'HTML';
    case '.png':
    case '.jpg':
    case '.jpeg':
    case '.gif':
    case '.svg':
    case '.webp':
      return 'Image';
    case '.woff':
    case '.woff2':
    case '.ttf':
    case '.eot':
      return 'Font';
    default:
      return 'Other';
  }
}

// Run the analysis
analyzeBundle();
