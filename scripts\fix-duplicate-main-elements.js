#!/usr/bin/env node

/**
 * Fix Duplicate Main Elements Script
 * 
 * This script finds and fixes duplicate main elements in page components
 * that are wrapped with StandardPageLayout (which already provides a main element)
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

const PAGES_DIR = 'src/pages';

function findFilesWithMainElements() {
  const pattern = path.join(PAGES_DIR, '**/*.tsx');
  const files = glob.sync(pattern);
  
  const filesWithMain = [];
  
  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check if file uses StandardPageLayout and has main elements
    const hasStandardPageLayout = content.includes('StandardPageLayout');
    const hasMainElement = content.includes('<main');
    
    if (hasStandardPageLayout && hasMainElement) {
      filesWithMain.push(file);
    }
  });
  
  return filesWithMain;
}

function fixMainElements(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Replace <main with <div
  const mainOpenRegex = /<main(\s+[^>]*)?>/g;
  if (mainOpenRegex.test(content)) {
    content = content.replace(mainOpenRegex, '<div$1>');
    modified = true;
  }
  
  // Replace </main> with </div>
  const mainCloseRegex = /<\/main>/g;
  if (mainCloseRegex.test(content)) {
    content = content.replace(mainCloseRegex, '</div>');
    modified = true;
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

function main() {
  console.log('🔍 Finding files with duplicate main elements...');
  
  const filesWithMain = findFilesWithMainElements();
  
  if (filesWithMain.length === 0) {
    console.log('✅ No duplicate main elements found!');
    return;
  }
  
  console.log(`📝 Found ${filesWithMain.length} files with potential duplicate main elements:`);
  filesWithMain.forEach(file => console.log(`   - ${file}`));
  
  console.log('\n🔧 Fixing duplicate main elements...');
  
  let fixedCount = 0;
  filesWithMain.forEach(file => {
    if (fixMainElements(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed ${fixedCount} files with duplicate main elements!`);
  
  if (fixedCount > 0) {
    console.log('\n⚠️  Please review the changes and run tests to ensure everything works correctly.');
  }
}

main();
