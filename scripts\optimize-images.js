#!/usr/bin/env node

/**
 * Image Optimization Script
 * Optimizes images for production deployment
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const IMAGES_DIR = path.join(__dirname, '../public/images');
const REPORT_FILE = path.join(__dirname, '../image-optimization-report.json');

// Size thresholds (in KB)
const THRESHOLDS = {
  jpg: {
    warning: 100,
    error: 200
  },
  png: {
    warning: 50,
    error: 100
  },
  webp: {
    warning: 80,
    error: 150
  }
};

/**
 * Get file size in KB
 */
function getFileSizeKB(filePath) {
  const stats = fs.statSync(filePath);
  return Math.round(stats.size / 1024);
}

/**
 * Get all image files
 */
function getAllImageFiles(dir) {
  const imageFiles = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (['.jpg', '.jpeg', '.png', '.webp'].includes(ext)) {
          imageFiles.push({
            path: fullPath,
            relativePath: path.relative(IMAGES_DIR, fullPath),
            name: item,
            extension: ext.slice(1),
            size: getFileSizeKB(fullPath)
          });
        }
      }
    }
  }
  
  scanDirectory(dir);
  return imageFiles;
}

/**
 * Analyze image sizes
 */
function analyzeImageSizes() {
  console.log('🔍 Analyzing image sizes...\n');
  
  if (!fs.existsSync(IMAGES_DIR)) {
    console.error('❌ Images directory not found:', IMAGES_DIR);
    process.exit(1);
  }
  
  const imageFiles = getAllImageFiles(IMAGES_DIR);
  const analysis = {
    total: imageFiles.length,
    totalSize: imageFiles.reduce((sum, file) => sum + file.size, 0),
    oversized: [],
    byExtension: {}
  };
  
  // Group by extension
  for (const file of imageFiles) {
    if (!analysis.byExtension[file.extension]) {
      analysis.byExtension[file.extension] = {
        count: 0,
        totalSize: 0,
        files: []
      };
    }
    
    analysis.byExtension[file.extension].count++;
    analysis.byExtension[file.extension].totalSize += file.size;
    analysis.byExtension[file.extension].files.push(file);
    
    // Check if oversized
    const threshold = THRESHOLDS[file.extension];
    if (threshold && file.size > threshold.warning) {
      analysis.oversized.push({
        ...file,
        severity: file.size > threshold.error ? 'error' : 'warning',
        threshold: threshold.warning
      });
    }
  }
  
  // Sort oversized by size (largest first)
  analysis.oversized.sort((a, b) => b.size - a.size);
  
  return analysis;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(analysis) {
  const recommendations = [];
  
  // Large PNG files
  const largePNGs = analysis.oversized.filter(f => f.extension === 'png' && f.size > 100);
  if (largePNGs.length > 0) {
    recommendations.push({
      type: 'png-optimization',
      severity: 'high',
      message: `${largePNGs.length} PNG files are over 100KB`,
      files: largePNGs.map(f => f.relativePath),
      suggestions: [
        'Convert large PNGs to WebP format',
        'Use PNG compression tools like pngquant',
        'Consider using JPEG for photographic images',
        'Implement responsive images with different sizes'
      ]
    });
  }
  
  // Large JPEG files
  const largeJPGs = analysis.oversized.filter(f => ['jpg', 'jpeg'].includes(f.extension) && f.size > 150);
  if (largeJPGs.length > 0) {
    recommendations.push({
      type: 'jpg-optimization',
      severity: 'medium',
      message: `${largeJPGs.length} JPEG files are over 150KB`,
      files: largeJPGs.map(f => f.relativePath),
      suggestions: [
        'Reduce JPEG quality to 80-85%',
        'Resize images to appropriate dimensions',
        'Use progressive JPEG encoding',
        'Convert to WebP for better compression'
      ]
    });
  }
  
  // Total size warning
  if (analysis.totalSize > 5000) {
    recommendations.push({
      type: 'total-size',
      severity: 'high',
      message: `Total image size (${analysis.totalSize}KB) is very large`,
      suggestions: [
        'Implement lazy loading for images',
        'Use CDN for image delivery',
        'Create multiple image sizes for responsive design',
        'Consider using image optimization services'
      ]
    });
  }
  
  return recommendations;
}

/**
 * Display analysis results
 */
function displayResults(analysis) {
  console.log('📊 IMAGE ANALYSIS RESULTS');
  console.log('='.repeat(40));
  console.log(`Total images: ${analysis.total}`);
  console.log(`Total size: ${analysis.totalSize}KB`);
  console.log(`Oversized images: ${analysis.oversized.length}`);
  
  console.log('\n📁 BY EXTENSION');
  console.log('-'.repeat(30));
  for (const [ext, data] of Object.entries(analysis.byExtension)) {
    console.log(`${ext.toUpperCase()}: ${data.count} files, ${data.totalSize}KB`);
  }
  
  if (analysis.oversized.length > 0) {
    console.log('\n⚠️  OVERSIZED IMAGES');
    console.log('-'.repeat(30));
    
    for (const file of analysis.oversized.slice(0, 10)) {
      const icon = file.severity === 'error' ? '❌' : '⚠️';
      console.log(`${icon} ${file.relativePath} (${file.size}KB)`);
    }
    
    if (analysis.oversized.length > 10) {
      console.log(`... and ${analysis.oversized.length - 10} more`);
    }
  }
}

/**
 * Main analysis function
 */
function main() {
  console.log('🖼️  IMAGE OPTIMIZATION ANALYSIS');
  console.log('='.repeat(50));
  
  const analysis = analyzeImageSizes();
  const recommendations = generateRecommendations(analysis);
  
  displayResults(analysis);
  
  if (recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    
    for (const rec of recommendations) {
      const icon = rec.severity === 'high' ? '🔴' : rec.severity === 'medium' ? '🟡' : '🟢';
      console.log(`\n${icon} ${rec.message}`);
      
      if (rec.suggestions) {
        for (const suggestion of rec.suggestions) {
          console.log(`   • ${suggestion}`);
        }
      }
    }
  }
  
  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    analysis,
    recommendations
  };
  
  fs.writeFileSync(REPORT_FILE, JSON.stringify(report, null, 2));
  console.log(`\n📄 Detailed report saved to: ${path.relative(process.cwd(), REPORT_FILE)}`);
  
  // Exit with error code if critical issues found
  const criticalIssues = recommendations.filter(r => r.severity === 'high');
  if (criticalIssues.length > 0) {
    console.log(`\n❌ Found ${criticalIssues.length} critical image optimization issues!`);
    process.exit(1);
  } else {
    console.log('\n✅ Image analysis completed successfully!');
  }
}

// Run main function
main();

export { analyzeImageSizes, generateRecommendations };
