#!/usr/bin/env node

/**
 * Production Configuration Validator
 * 
 * This script validates that all production environment variables are properly configured
 * and that no development-only settings are enabled in production builds.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * Parse environment file
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};

  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
      }
    }
  });

  return env;
}

/**
 * Validate production environment
 */
function validateProductionEnvironment() {
  console.log(`${colors.blue}${colors.bold}🔍 Production Configuration Validator${colors.reset}\n`);

  const errors = [];
  const warnings = [];
  const info = [];

  // Load production environment
  const prodEnvPath = path.join(__dirname, '../.env.production');
  const prodEnv = parseEnvFile(prodEnvPath);

  // Required production variables
  const requiredVars = [
    'VITE_APP_ENV',
    'VITE_SITE_URL',
    'VITE_PRACTICE_PHONE',
    'VITE_PRACTICE_EMAIL'
  ];

  // Check required variables
  requiredVars.forEach(varName => {
    if (!prodEnv[varName] || prodEnv[varName].trim() === '') {
      errors.push(`Missing required variable: ${varName}`);
    }
  });

  // Check for placeholder values
  const placeholderPatterns = [
    /^G-XXXXXXXXXX$/,
    /^GTM-XXXXXXX$/,
    /^AIzaSy[X]+$/,
    /^service_[x]+$/,
    /^template_[x]+$/,
    /^[x]+$/,
    /^https:\/\/[x]+@sentry\.io\/[x]+$/
  ];

  Object.entries(prodEnv).forEach(([key, value]) => {
    if (value && placeholderPatterns.some(pattern => pattern.test(value))) {
      warnings.push(`Placeholder value detected for ${key}: ${value}`);
    }
  });

  // Check debug settings
  if (prodEnv.VITE_DEBUG_MODE === 'true') {
    errors.push('VITE_DEBUG_MODE must be false in production');
  }

  if (prodEnv.VITE_SHOW_PERFORMANCE_METRICS === 'true') {
    errors.push('VITE_SHOW_PERFORMANCE_METRICS must be false in production');
  }

  // Check environment setting
  if (prodEnv.VITE_APP_ENV !== 'production') {
    errors.push('VITE_APP_ENV must be set to "production"');
  }

  // Check security settings
  if (prodEnv.VITE_ENABLE_CSP !== 'true') {
    warnings.push('VITE_ENABLE_CSP should be true in production');
  }

  if (prodEnv.VITE_ENABLE_HSTS !== 'true') {
    warnings.push('VITE_ENABLE_HSTS should be true in production');
  }

  // Check recommended variables
  const recommendedVars = [
    'VITE_GOOGLE_ANALYTICS_ID',
    'VITE_SENTRY_DSN',
    'VITE_GOOGLE_MAPS_API_KEY'
  ];

  recommendedVars.forEach(varName => {
    if (!prodEnv[varName] || prodEnv[varName].trim() === '') {
      info.push(`Recommended variable not set: ${varName}`);
    }
  });

  // Validate URLs
  const urlVars = ['VITE_SITE_URL', 'VITE_API_BASE_URL', 'VITE_CDN_URL'];
  urlVars.forEach(varName => {
    const value = prodEnv[varName];
    if (value && value.trim() !== '') {
      try {
        new URL(value);
        if (!value.startsWith('https://')) {
          warnings.push(`${varName} should use HTTPS: ${value}`);
        }
      } catch {
        errors.push(`Invalid URL format for ${varName}: ${value}`);
      }
    }
  });

  // Validate email addresses
  const emailVars = ['VITE_PRACTICE_EMAIL', 'VITE_ARGUS_EMAIL'];
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  emailVars.forEach(varName => {
    const value = prodEnv[varName];
    if (value && value.trim() !== '' && !emailRegex.test(value)) {
      errors.push(`Invalid email format for ${varName}: ${value}`);
    }
  });

  // Output results
  console.log(`${colors.bold}Validation Results:${colors.reset}\n`);

  if (errors.length > 0) {
    console.log(`${colors.red}${colors.bold}❌ ERRORS (${errors.length}):${colors.reset}`);
    errors.forEach(error => console.log(`${colors.red}  • ${error}${colors.reset}`));
    console.log();
  }

  if (warnings.length > 0) {
    console.log(`${colors.yellow}${colors.bold}⚠️  WARNINGS (${warnings.length}):${colors.reset}`);
    warnings.forEach(warning => console.log(`${colors.yellow}  • ${warning}${colors.reset}`));
    console.log();
  }

  if (info.length > 0) {
    console.log(`${colors.blue}${colors.bold}ℹ️  INFO (${info.length}):${colors.reset}`);
    info.forEach(infoItem => console.log(`${colors.blue}  • ${infoItem}${colors.reset}`));
    console.log();
  }

  if (errors.length === 0 && warnings.length === 0) {
    console.log(`${colors.green}${colors.bold}✅ Production configuration is valid!${colors.reset}\n`);
  } else if (errors.length === 0) {
    console.log(`${colors.yellow}${colors.bold}⚠️  Production configuration has warnings but is functional${colors.reset}\n`);
  } else {
    console.log(`${colors.red}${colors.bold}❌ Production configuration has critical errors${colors.reset}\n`);
  }

  // Exit with appropriate code
  process.exit(errors.length > 0 ? 1 : 0);
}

// Run validation
validateProductionEnvironment();
