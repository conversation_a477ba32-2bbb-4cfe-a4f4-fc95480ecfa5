/**
 * Device Context Performance Tests
 * Example tests demonstrating the testing framework
 */

import { renderHook } from '@testing-library/react-hooks';

import {
  useIsMobile,
  useIsTablet,
  useDeviceDetection,
  useViewportSize,
  useResponsiveValue
} from '@/contexts/DeviceContext';
import {
  deviceContextTester,
  testMobileResponsiveness,
  testDesktopResponsiveness,
  createDeviceTestWrapper,
  DeviceTestConfig
} from '@/lib/device-context-testing';

describe('Device Context Performance Tests', () => {
  beforeEach(() => {
    deviceContextTester.clearResults();
  });

  describe('Selective Hook Performance', () => {
    test('useIsMobile should have minimal re-renders', async () => {
      const result = await testMobileResponsiveness(() => {
        const isMobile = useIsMobile();
        return isMobile;
      });

      expect(result.optimizationScore).toBeGreaterThan(70);
      expect(result.renderCount).toBeLessThan(10);
      expect(result.suggestions).not.toContain('Consider using selective hooks instead of full device context');
    });

    test('useViewportSize should handle resize efficiently', async () => {
      const wrapper = createDeviceTestWrapper({ width: 1024, height: 768 });
      let renderCount = 0;

      const { rerender } = renderHook(() => {
        renderCount++;
        return useViewportSize();
      }, { wrapper });

      // Simulate multiple resizes
      for (let i = 0; i < 5; i++) {
        window.innerWidth = 1024 + i * 100;
        window.dispatchEvent(new Event('resize'));
        rerender();
      }

      // Should have reasonable render count due to debouncing
      expect(renderCount).toBeLessThan(15);
    });

    test('useResponsiveValue should optimize value selection', async () => {
      const result = await testDesktopResponsiveness(() => {
        return useResponsiveValue({
          mobile: 'small',
          tablet: 'medium',
          desktop: 'large'
        });
      });

      expect(result.optimizationScore).toBeGreaterThan(60);
      expect(result.averageRenderTime).toBeLessThan(5);
    });
  });

  describe('Hook Comparison Tests', () => {
    test('selective hooks vs full context performance', async () => {
      // Create test components that properly use hooks
      const _TestIsMobileComponent = () => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const isMobile = useIsMobile();
        return isMobile;
      };

      const _TestDeviceDetectionComponent = () => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const { isMobile } = useDeviceDetection();
        return isMobile;
      };

      const testSuite = await deviceContextTester.runTestSuite([
        {
          name: 'useIsMobile_selective',
          test: () => {
            const wrapper = createDeviceTestWrapper({ width: 375, height: 667 });
            const { result } = renderHook(() => useIsMobile(), { wrapper });
            return result.current;
          },
          config: { width: 375, height: 667 }
        },
        {
          name: 'useDeviceDetection_full',
          test: () => {
            const wrapper = createDeviceTestWrapper({ width: 375, height: 667 });
            const { result } = renderHook(() => useDeviceDetection(), { wrapper });
            return result.current.isMobile;
          },
          config: { width: 375, height: 667 }
        }
      ], 'SelectiveVsFullContext');

      const selectiveResult = testSuite.results.find(r => r.componentName === 'useIsMobile_selective');
      const fullContextResult = testSuite.results.find(r => r.componentName === 'useDeviceDetection_full');

      // Selective hook should perform better
      expect(selectiveResult?.optimizationScore).toBeGreaterThan(fullContextResult?.optimizationScore || 0);
    });
  });

  describe('Component Optimization Tests', () => {
    test('memoized components should have better performance', async () => {
      // Create proper test components
      const _UnmemoizedTestComponent = () => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const isMobile = useIsMobile();
        // Simulate expensive operation
        const expensiveValue = Array.from({ length: 1000 }, (_, i) => i).reduce((a, b) => a + b, 0);
        return { isMobile, expensiveValue };
      };

      const _MemoizedTestComponent = () => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const isMobile = useIsMobile();
        // Simulate memoized expensive operation
        const expensiveValue = 499500; // Pre-calculated value
        return { isMobile, expensiveValue };
      };

      // Test using renderHook instead of calling hooks directly
      const unmemoizedTest = async () => {
        const wrapper = createDeviceTestWrapper({ width: 1024, height: 768 });
        const { result } = renderHook(() => {
          const isMobile = useIsMobile();
          const expensiveValue = Array.from({ length: 1000 }, (_, i) => i).reduce((a, b) => a + b, 0);
          return { isMobile, expensiveValue };
        }, { wrapper });
        return result.current;
      };

      const memoizedTest = async () => {
        const wrapper = createDeviceTestWrapper({ width: 1024, height: 768 });
        const { result } = renderHook(() => {
          const isMobile = useIsMobile();
          const expensiveValue = 499500; // Pre-calculated value
          return { isMobile, expensiveValue };
        }, { wrapper });
        return result.current;
      };

      const unmemoizedResult = await deviceContextTester.testComponentPerformance(
        unmemoizedTest,
        {},
        'UnmemoizedComponent'
      );

      const memoizedResult = await deviceContextTester.testComponentPerformance(
        memoizedTest,
        {},
        'MemoizedComponent'
      );

      expect(memoizedResult.averageRenderTime).toBeLessThan(unmemoizedResult.averageRenderTime);
      expect(memoizedResult.optimizationScore).toBeGreaterThan(unmemoizedResult.optimizationScore);
    });
  });

  describe('Device Change Scenarios', () => {
    test('orientation change should trigger minimal re-renders', async () => {
      const wrapper = createDeviceTestWrapper({ width: 768, height: 1024 });
      let renderCount = 0;

      const { rerender } = renderHook(() => {
        renderCount++;
        return useIsMobile();
      }, { wrapper });

      // Simulate orientation change (tablet portrait to landscape)
      window.innerWidth = 1024;
      window.innerHeight = 768;
      window.dispatchEvent(new Event('orientationchange'));
      rerender();

      // Should not cause excessive re-renders for mobile detection
      expect(renderCount).toBeLessThan(5);
    });

    test('multiple rapid resizes should be debounced', async () => {
      const wrapper = createDeviceTestWrapper({ 
        width: 1024, 
        height: 768,
        debounceDelay: 100 // Enable debouncing
      });
      let renderCount = 0;

      const { rerender } = renderHook(() => {
        renderCount++;
        return useViewportSize();
      }, { wrapper });

      // Simulate rapid resizes
      for (let i = 0; i < 10; i++) {
        window.innerWidth = 1024 + i;
        window.dispatchEvent(new Event('resize'));
        rerender();
      }

      // Should have fewer renders due to debouncing
      expect(renderCount).toBeLessThan(15);
    });
  });

  describe('Performance Benchmarks', () => {
    test('device context should meet performance thresholds', async () => {
      const testSuite = await deviceContextTester.runTestSuite([
        {
          name: 'useIsMobile',
          test: () => {
            const wrapper = createDeviceTestWrapper({ width: 375, height: 667 });
            const { result } = renderHook(() => useIsMobile(), { wrapper });
            return result.current;
          }
        },
        {
          name: 'useIsTablet',
          test: () => {
            const wrapper = createDeviceTestWrapper({ width: 768, height: 1024 });
            const { result } = renderHook(() => useIsTablet(), { wrapper });
            return result.current;
          }
        },
        {
          name: 'useViewportSize',
          test: () => {
            const wrapper = createDeviceTestWrapper({ width: 1024, height: 768 });
            const { result } = renderHook(() => useViewportSize(), { wrapper });
            return result.current;
          }
        }
      ], 'PerformanceBenchmark');

      // Overall performance should be good
      expect(testSuite.overallScore).toBeGreaterThan(70);
      
      // No critical issues
      expect(testSuite.summary.criticalIssues.length).toBe(0);
      
      // All components should meet minimum performance
      testSuite.results.forEach(result => {
        expect(result.optimizationScore).toBeGreaterThan(50);
        expect(result.averageRenderTime).toBeLessThan(10);
      });
    });
  });

  describe('Error Handling', () => {
    test('device context should handle invalid configurations gracefully', () => {
      const invalidConfigs: DeviceTestConfig[] = [
        { width: -1, height: -1 },
        { width: 0, height: 0 },
        { debounceDelay: -100 }
      ];

      invalidConfigs.forEach(config => {
        expect(() => {
          createDeviceTestWrapper(config);
        }).not.toThrow();
      });
    });
  });

  describe('Memory Leaks', () => {
    test('device context should not leak memory', async () => {
      const initialMemory = (performance as Performance & { memory?: { usedJSHeapSize?: number } }).memory?.usedJSHeapSize || 0;
      
      // Create and destroy multiple wrappers
      for (let i = 0; i < 100; i++) {
        const wrapper = createDeviceTestWrapper({ width: 1024 + i, height: 768 + i });
        const { unmount } = renderHook(() => useIsMobile(), { wrapper });
        unmount();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = (performance as Performance & { memory?: { usedJSHeapSize?: number } }).memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });
});

describe('Integration Tests', () => {
  test('complete device context workflow', async () => {
    const testSuite = await deviceContextTester.runTestSuite([
      {
        name: 'MobileWorkflow',
        test: () => {
          const wrapper = createDeviceTestWrapper({ width: 375, height: 667, touchSupport: true });
          const { result } = renderHook(() => {
            const isMobile = useIsMobile();
            const { width, height } = useViewportSize();
            const value = useResponsiveValue({
              mobile: 'mobile-value',
              desktop: 'desktop-value'
            });
            return { isMobile, width, height, value };
          }, { wrapper });
          return result.current;
        },
        config: { width: 375, height: 667, touchSupport: true }
      },
      {
        name: 'DesktopWorkflow',
        test: () => {
          const wrapper = createDeviceTestWrapper({ width: 1024, height: 768 });
          const { result } = renderHook(() => {
            const isMobile = useIsMobile();
            const { width, height } = useViewportSize();
            const value = useResponsiveValue({
              mobile: 'mobile-value',
              desktop: 'desktop-value'
            });
            return { isMobile, width, height, value };
          }, { wrapper });
          return result.current;
        },
        config: { width: 1920, height: 1080, hoverSupport: true }
      }
    ], 'CompleteWorkflow');

    // Generate and validate report
    const report = deviceContextTester.generateReport(testSuite);
    expect(report).toContain('Device Context Test Report');
    expect(report).toContain('MobileWorkflow');
    expect(report).toContain('DesktopWorkflow');
    
    // Overall performance should be acceptable
    expect(testSuite.overallScore).toBeGreaterThan(60);
  });
});
