import React from 'react';

import { Card } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface CauseRiskFactor {
  icon: LucideIcon;
  title: string;
  description: string;
  category?: 'primary' | 'secondary' | 'risk-factor';
  iconColor?: string;
}

export interface CausesAndRiskFactorsProps {
  title?: string;
  primaryCauses?: CauseRiskFactor[];
  riskFactors?: CauseRiskFactor[];
  className?: string;
}

const CausesAndRiskFactors: React.FC<CausesAndRiskFactorsProps> = ({
  title = "Causes and Risk Factors",
  primaryCauses = [],
  riskFactors = [],
  className
}) => {
  const deviceInfo = useDeviceDetection();

  const renderFactorCard = (factor: CauseRiskFactor, index: number) => {
    const IconComponent = factor.icon;
    const iconColorClass = factor.iconColor || 'text-primary';

    return (
      <Card key={index} className="p-4">
        <div className="flex items-start gap-3">
          <IconComponent className={cn("h-5 w-5 mt-1 flex-shrink-0", iconColorClass)} />
          <div>
            <h4 className="font-semibold mb-2">{factor.title}</h4>
            <p className="text-sm text-muted-foreground">
              {factor.description}
            </p>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={cn(
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}><div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        <h2 className={cn(
          "font-bold text-center mb-12",
          deviceInfo.isMobile ? "text-2xl mb-8" : "text-3xl"
        )}>
          {title}
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {primaryCauses.length > 0 && (
            <div>
              <h3 className="text-xl font-bold mb-6">Primary Causes</h3>
              <div className="space-y-4">
                {primaryCauses.map((cause, index) => renderFactorCard(cause, index))}
              </div>
            </div>
          )}

          {riskFactors.length > 0 && (
            <div>
              <h3 className="text-xl font-bold mb-6">Risk Factors</h3>
              <div className="space-y-4">
                {riskFactors.map((factor, index) => renderFactorCard(factor, index))}
              </div>
            </div>
          )}
        </div>
      </div>        </div>
  );
};

export default CausesAndRiskFactors;