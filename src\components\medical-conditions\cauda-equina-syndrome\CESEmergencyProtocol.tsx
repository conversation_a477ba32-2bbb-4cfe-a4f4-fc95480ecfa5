import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, CheckCircle, Clock, Phone, Scissors } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ProtocolStep {
  step: string;
  action: string;
  timeframe: string;
  rationale: string;
}

interface Treatment {
  name: string;
  description: string;
  indications: string[];
  advantages: string[];
  disadvantages: string[];
  successRate: string;
  timeframe: string;
  complications: string[];
}

interface CESEmergencyProtocolProps {
  title: string;
  description: string;
  protocol: ProtocolStep[];
  treatments: Treatment[];
}

export function CESEmergencyProtocol({ 
  title, 
  description, 
  protocol,
  treatments 
}: CESEmergencyProtocolProps) {
  const deviceInfo = useDeviceDetection();
  const [activeTab, setActiveTab] = useState('protocol');

  const getTimeframeHours = (timeframe: string) => {
    if (timeframe.includes('minutes')) return 1;
    if (timeframe.includes('30 minutes')) return 2;
    if (timeframe.includes('6 hours')) return 25;
    if (timeframe.includes('2 hours')) return 8;
    if (timeframe.includes('48-72 hours')) return 100;
    return 50;
  };

  const getUrgencyColor = (timeframe: string) => {
    if (timeframe.includes('minutes')) return 'bg-muted';
    if (timeframe.includes('30 minutes')) return 'bg-muted/80';
    if (timeframe.includes('6 hours')) return 'bg-info';
    if (timeframe.includes('2 hours')) return 'bg-info';
    return 'bg-info';
  };

  const getStepIcon = (step: string) => {
    if (step.includes('Recognition')) return AlertTriangle;
    if (step.includes('Assessment')) return Stethoscope;
    if (step.includes('Imaging')) return Activity;
    if (step.includes('Consultation')) return Phone;
    return Scissors;
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-2"
          )}>
            <TabsTrigger 
              value="protocol"
              className={cn(
                "text-center",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Emergency Protocol
            </TabsTrigger>
            <TabsTrigger 
              value="treatments"
              className={cn(
                "text-center",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Treatment Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="protocol" className="space-y-8">
            {/* Critical Timeline */}
            <Card className="bg-muted/50 dark:bg-muted/30 border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-foreground">
                  <Clock className="h-5 w-5" />
                  Critical Time Windows
                </CardTitle>
                <CardDescription className="text-foreground">
                  Time is critical in cauda equina syndrome - every hour counts for neurological recovery
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "grid gap-4",
                  deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                )}>
                  <div className="text-center p-4 medical-card rounded-lg border border-border/70">
                    <div className="text-enhanced-heading text-2xl font-bold text-foreground mb-2">0-48h</div>
                    <div className="text-sm font-medium mb-1">Optimal Window</div>
                    <div className="text-xs text-muted-foreground">Best recovery outcomes</div>
                  </div>
                  <div className="text-center p-4 medical-card rounded-lg border border-info/30">
                    <div className="text-enhanced-heading text-2xl font-bold text-info dark:text-info mb-2">48-72h</div>
                    <div className="text-sm font-medium mb-1">Critical Window</div>
                    <div className="text-xs text-muted-foreground">Reduced recovery potential</div>
                  </div>
                  <div className="text-center p-4 medical-card rounded-lg border border-border">
                    <div className="text-enhanced-heading text-2xl font-bold text-muted-foreground mb-2">&gt;72h</div>
                    <div className="text-sm font-medium mb-1">Late Intervention</div>
                    <div className="text-xs text-muted-foreground">Limited recovery expected</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Protocol Steps */}
            <div className="space-y-6">
              {protocol.map((step, index) => {
                const Icon = getStepIcon(step.step);
                const progressValue = getTimeframeHours(step.timeframe);
                const urgencyColor = getUrgencyColor(step.timeframe);
                
                return (
                  <Card key={index} className="relative overflow-hidden">
                    <div className={cn("absolute left-0 top-0 bottom-0 w-1", urgencyColor)}></div>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-full bg-muted/30">
                            <Icon className="h-5 w-5 text-foreground" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">Step {index + 1}: {step.step}</CardTitle>
                            <CardDescription className="text-sm">{step.action}</CardDescription>
                          </div>
                        </div>
                        <Badge variant="muted">
                          {step.timeframe}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-sm mb-2">Urgency Level</h4>
                          <Progress value={progressValue} className="h-2" />
                        </div>
                        
                        <div className="bg-primary/10 border border-primary/50 rounded p-3">
                          <h4 className="font-medium text-sm text-primary mb-1">Clinical Rationale</h4>
                          <p className="text-sm text-primary/90">{step.rationale}</p>
                        </div>

                        {index < protocol.length - 1 && (
                          <div className="flex justify-center">
                            <ArrowRight className="h-5 w-5 text-foreground" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Emergency Contacts */}
            <Card className="bg-muted/50 dark:bg-muted/30 border-border">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-foreground">
                  <Phone className="h-5 w-5" />
                  Emergency Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "grid gap-4",
                  deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                )}>
                  <div className="medical-card p-4 rounded-lg border border-border/70">
                    <h4 className="font-semibold text-foreground mb-2">Emergency Services</h4>
                    <p className="text-sm text-foreground mb-2">
                      <strong>Call 000 immediately</strong> for ambulance transport
                    </p>
                    <p className="text-xs text-foreground/80">
                      Tell them: "Suspected cauda equina syndrome - surgical emergency"
                    </p>
                  </div>
                  <div className="medical-card p-4 rounded-lg border border-border/70">
                    <h4 className="font-semibold text-foreground mb-2">Emergency Department</h4>
                    <p className="text-sm text-foreground mb-2">
                      Go to nearest hospital with neurosurgical services
                    </p>
                    <p className="text-xs text-foreground">
                      Do not wait - time is critical for recovery
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="treatments" className="space-y-6">
            {treatments.map((treatment, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Scissors className="h-5 w-5 text-primary" />
                    {treatment.name}
                  </CardTitle>
                  <CardDescription>{treatment.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Key Metrics */}
                  <div className={cn(
                    "grid gap-6",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                  )}>
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-success" />
                        Success Rate
                      </h4>
                      <p className="text-sm text-muted-foreground">{treatment.successRate}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Clock className="h-4 w-4 text-info" />
                        Timeframe
                      </h4>
                      <p className="text-sm text-muted-foreground">{treatment.timeframe}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-info" />
                        Risk Level
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {treatment.complications.length} potential complications
                      </p>
                    </div>
                  </div>

                  {/* Treatment Details */}
                  <div className={cn(
                    "grid gap-6",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                  )}>
                    <div>
                      <h4 className="font-semibold mb-3 text-info">Indications</h4>
                      <ul className="space-y-2">
                        {treatment.indications.slice(0, 4).map((indication, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{indication}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3 text-success">Advantages</h4>
                      <ul className="space-y-2">
                        {treatment.advantages.slice(0, 4).map((advantage, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{advantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-3 text-foreground">Considerations</h4>
                      <ul className="space-y-2">
                        {treatment.disadvantages.slice(0, 4).map((disadvantage, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                            <span className="text-sm">{disadvantage}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Complications */}
                  <div>
                    <h4 className="font-semibold mb-3 text-info">Potential Complications</h4>
                    <div className={cn(
                      "grid gap-2",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      {treatment.complications.map((complication, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-info rounded-full flex-shrink-0" />
                          <span className="text-sm">{complication}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Clinical Notes */}
                  <div className="bg-info-light border border-info/30 rounded-lg p-4">
                    <h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>
                    <p className="text-sm text-foreground">
                      {treatment.name.includes('Emergency') && 
                        "Emergency surgical decompression is the gold standard treatment for cauda equina syndrome. The timing of surgery is critical - outcomes are significantly better when performed within 48-72 hours of symptom onset."
                      }
                      {treatment.name.includes('Conservative') && 
                        "Conservative management is rarely appropriate for cauda equina syndrome and is only considered in very specific circumstances. The risk of permanent neurological damage makes surgical intervention the preferred approach."
                      }
                      {treatment.name.includes('Rehabilitation') && 
                        "Comprehensive rehabilitation is essential for all patients following cauda equina syndrome, regardless of surgical outcomes. Early and intensive rehabilitation maximises functional recovery and quality of life."
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-muted/30 dark:bg-muted/20 border-border/20">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2 text-foreground">Emergency Cauda Equina Treatment</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team provides 24/7 emergency treatment for cauda equina syndrome.
                Time-critical surgical intervention and comprehensive rehabilitation for optimal outcomes.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"} variant="muted" >
                Emergency Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default CESEmergencyProtocol;
