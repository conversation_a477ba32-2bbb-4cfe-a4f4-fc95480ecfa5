import { Activity, AlertTriangle, CheckCircle, Clock, Target, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Treatment {
  name: string;
  description: string;
  indications: string[];
  advantages: string[];
  disadvantages: string[];
  successRate: string;
  recoveryTime: string;
  sideEffects: string[];
}

interface CavernomaTreatmentComparisonProps {
  title: string;
  description: string;
  treatments: Treatment[];
}

export function CavernomaTreatmentComparison({ 
  title, 
  description, 
  treatments 
}: CavernomaTreatmentComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>(treatments[0]?.name || '');
  const [comparisonMode, setComparisonMode] = useState(false);

  const _selectedTreatment_obj = treatments.find(treatment => treatment.name === selectedTreatment);

  const getSuccessRateNumber = (successRate: string) => {
    const match = successRate.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 0;
  };

  const getInvasivenessLevel = (treatmentName: string) => {
    if (treatmentName.includes('Observation')) return { level: 'None', value: 0, colour: 'bg-success' };
    if (treatmentName.includes('Surgical') || treatmentName.includes('Resection')) return { level: 'High', value: 80, colour: 'bg-muted' };
    return { level: 'Variable', value: 50, colour: 'bg-info' };
  };

  const getRecoveryTimeValue = (recoveryTime: string) => {
    if (recoveryTime.includes('No recovery')) return { time: 'None', value: 0, colour: 'bg-success' };
    if (recoveryTime.includes('2-6 weeks')) return { time: '2-6 weeks', value: 60, colour: 'bg-info' };
    if (recoveryTime.includes('weeks')) return { time: 'Weeks', value: 40, colour: 'bg-info' };
    return { time: 'Variable', value: 30, colour: 'bg-info' };
  };

  const getTreatmentIcon = (treatmentName: string) => {
    if (treatmentName.includes('Observation')) return Eye;
    if (treatmentName.includes('Surgical') || treatmentName.includes('Resection')) return Scissors;
    return Activity;
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* View Toggle */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-muted rounded-lg p-1">
            <Button
              variant={!comparisonMode ? "default" : "ghost"}
              size="sm"
              onClick={() => setComparisonMode(false)}
            >
              Detailed View
            </Button>
            <Button
              variant={comparisonMode ? "default" : "ghost"}
              size="sm"
              onClick={() => setComparisonMode(true)}
            >
              Comparison View
            </Button>
          </div>
        </div>

        {!comparisonMode ? (
          /* Detailed View */
          <Tabs value={selectedTreatment} onValueChange={setSelectedTreatment} className="w-full">
            <TabsList className={cn(
              "grid w-full mb-8",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(treatments.length, 2)}`
            )}>
              {treatments.map((treatment) => (
                <TabsTrigger 
                  key={treatment.name} 
                  value={treatment.name}
                  className={cn(
                    "text-center",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {treatment.name.replace('Observation and Monitoring', 'Observation')}
                </TabsTrigger>
              ))}
            </TabsList>

            {treatments.map((treatment) => {
              const Icon = getTreatmentIcon(treatment.name);
              
              return (
                <TabsContent key={treatment.name} value={treatment.name} className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Icon className="h-5 w-5 text-primary" />
                        {treatment.name}
                      </CardTitle>
                      <CardDescription>{treatment.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Key Metrics */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-success" />
                            Success Rate
                          </h4>
                          <div className="space-y-2">
                            <Progress value={getSuccessRateNumber(treatment.successRate)} className="h-2" />
                            <p className="text-sm text-muted-foreground">{treatment.successRate}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <Activity className="h-4 w-4 text-info" />
                            Invasiveness
                          </h4>
                          <div className="space-y-2">
                            <Progress 
                              value={getInvasivenessLevel(treatment.name).value} 
                              className="h-2"
                            />
                            <p className="text-sm text-muted-foreground">
                              {getInvasivenessLevel(treatment.name).level}
                            </p>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-2 flex items-center gap-2">
                            <Clock className="h-4 w-4 text-medical-blue" />
                            Recovery Time
                          </h4>
                          <div className="space-y-2">
                            <Progress 
                              value={getRecoveryTimeValue(treatment.recoveryTime).value} 
                              className="h-2"
                            />
                            <p className="text-sm text-muted-foreground">{treatment.recoveryTime}</p>
                          </div>
                        </div>
                      </div>

                      {/* Indications, Advantages, Disadvantages */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                            <Target className="h-4 w-4" />
                            Best For
                          </h4>
                          <ul className="space-y-2">
                            {treatment.indications.map((indication, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{indication}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-success">
                            <CheckCircle className="h-4 w-4" />
                            Advantages
                          </h4>
                          <ul className="space-y-2">
                            {treatment.advantages.map((advantage, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2 text-foreground">
                            <AlertTriangle className="h-4 w-4" />
                            Considerations
                          </h4>
                          <ul className="space-y-2">
                            {treatment.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm">{disadvantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Side Effects */}
                      <div>
                        <h4 className="font-semibold mb-3 flex items-center gap-2 text-info">
                          <AlertTriangle className="h-4 w-4" />
                          Potential Side Effects
                        </h4>
                        <div className={cn(
                          "grid gap-2",
                          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                        )}>
                          {treatment.sideEffects.map((effect, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full flex-shrink-0" />
                              <span className="text-sm">{effect}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Clinical Notes */}
                      <div className="bg-info-light border border-info/30 rounded-lg p-4">
                        <h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>
                        <p className="text-sm text-foreground">
                          {treatment.name.includes('Observation') && 
                            "Observation is appropriate for asymptomatic cavernomas, especially in deep or eloquent brain locations. Regular MRI monitoring is essential to detect changes that might warrant intervention."
                          }
                          {treatment.name.includes('Surgical') && 
                            "Surgical resection eliminates future bleeding risk and may cure seizures. The decision for surgery depends on bleeding history, symptoms, location, and patient factors. Complete resection is the goal when safely achievable."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        ) : (
          /* Comparison View */
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Treatment Comparison</CardTitle>
                <CardDescription>
                  Compare key metrics across treatment options
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Treatment</th>
                        <th className="text-left p-2">Success Rate</th>
                        <th className="text-left p-2">Invasiveness</th>
                        <th className="text-left p-2">Recovery</th>
                        <th className="text-left p-2">Best For</th>
                      </tr>
                    </thead>
                    <tbody>
                      {treatments.map((treatment, index) => (
                        <tr key={index} className="border-b">
                          <td className="p-2 font-medium">
                            {treatment.name.replace('Observation and Monitoring', 'Observation')}
                          </td>
                          <td className="p-2">
                            <Badge variant="secondary">{treatment.successRate}</Badge>
                          </td>
                          <td className="p-2">
                            <Badge className={cn(
                              "text-primary-foreground",
                              getInvasivenessLevel(treatment.name).colour
                            )}>
                              {getInvasivenessLevel(treatment.name).level}
                            </Badge>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{treatment.recoveryTime}</span>
                          </td>
                          <td className="p-2">
                            <span className="text-sm">{treatment.indications[0]}</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Decision Support */}
        <div className="mt-12">
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Choosing the Right Treatment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-4",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold mb-2">Consider Observation if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Asymptomatic cavernoma</li>
                    <li>• Deep or eloquent brain location</li>
                    <li>• No bleeding history</li>
                    <li>• High surgical risk</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Consider Surgery if:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• History of bleeding</li>
                    <li>• Medically refractory seizures</li>
                    <li>• Accessible location</li>
                    <li>• Progressive symptoms</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Expert Cavernoma Treatment</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team specialises in cavernoma treatment and will help 
                you choose the best treatment option based on your specific cavernoma characteristics and personal factors.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"} >
                Schedule Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default CavernomaTreatmentComparison;
