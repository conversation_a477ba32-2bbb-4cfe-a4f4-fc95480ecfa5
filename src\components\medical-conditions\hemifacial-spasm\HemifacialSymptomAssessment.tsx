import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Left, ArrowRight, Brain, CheckCircle, TrendingUp, Zap } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'prompt';
  nextSteps: string[];
  symptoms: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'facial-spasms',
    question: 'Do you experience involuntary muscle contractions on one side of your face?',
    description: 'Unilateral facial spasms are the hallmark of hemifacial spasm',
    options: [
      { value: 'continuous-spasms', label: 'Yes, continuous or near-continuous spasms', score: 5 },
      { value: 'frequent-spasms', label: 'Yes, frequent spasms throughout the day', score: 4 },
      { value: 'intermittent-spasms', label: 'Yes, intermittent spasms', score: 3 },
      { value: 'no-spasms', label: 'No facial spasms', score: 0 }
    ]
  },
  {
    id: 'eyelid-involvement',
    question: 'Do you have eyelid twitching or forceful eye closure?',
    description: 'Eyelid involvement is typically the first symptom of hemifacial spasm',
    options: [
      { value: 'forceful-closure', label: 'Yes, forceful eye closure that interferes with vision', score: 4 },
      { value: 'frequent-twitching', label: 'Yes, frequent eyelid twitching', score: 3 },
      { value: 'occasional-twitching', label: 'Yes, occasional eyelid twitching', score: 2 },
      { value: 'no-eyelid-symptoms', label: 'No eyelid symptoms', score: 0 }
    ]
  },
  {
    id: 'progression-pattern',
    question: 'How have your facial symptoms progressed over time?',
    description: 'Hemifacial spasm typically starts around the eye and spreads downward',
    options: [
      { value: 'eye-to-face', label: 'Started around eye and spread to cheek/mouth', score: 4 },
      { value: 'worsening-eye', label: 'Started with eyelid twitching that has worsened', score: 3 },
      { value: 'stable-symptoms', label: 'Symptoms have remained stable', score: 1 },
      { value: 'no-progression', label: 'No clear progression pattern', score: 0 }
    ]
  },
  {
    id: 'unilateral-nature',
    question: 'Are your facial symptoms limited to one side of your face?',
    description: 'Hemifacial spasm is characteristically unilateral (one-sided)',
    options: [
      { value: 'strictly-unilateral', label: 'Yes, strictly limited to one side', score: 4 },
      { value: 'predominantly-unilateral', label: 'Predominantly one side with occasional other side', score: 2 },
      { value: 'bilateral-symptoms', label: 'Both sides of face affected', score: 0 },
      { value: 'uncertain-pattern', label: 'Uncertain about the pattern', score: 1 }
    ]
  },
  {
    id: 'trigger-factors',
    question: 'Do certain activities or situations trigger or worsen your facial spasms?',
    description: 'Stress, fatigue, and certain movements can trigger hemifacial spasm',
    options: [
      { value: 'multiple-triggers', label: 'Yes, stress, fatigue, and specific movements trigger spasms', score: 3 },
      { value: 'stress-triggers', label: 'Yes, mainly stress or emotional situations', score: 2 },
      { value: 'occasional-triggers', label: 'Occasionally triggered by certain activities', score: 1 },
      { value: 'no-triggers', label: 'No clear triggers identified', score: 0 }
    ]
  },
  {
    id: 'functional-impact',
    question: 'How do your facial symptoms affect your daily activities?',
    description: 'Assessing functional impact helps determine treatment urgency',
    options: [
      { value: 'severe-impact', label: 'Severely affects vision, eating, speaking, or social activities', score: 4 },
      { value: 'moderate-impact', label: 'Moderately affects daily activities and social confidence', score: 3 },
      { value: 'mild-impact', label: 'Mild impact, mainly cosmetic concern', score: 2 },
      { value: 'no-impact', label: 'No significant impact on daily activities', score: 0 }
    ]
  }
];

export function HemifacialSymptomAssessment() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'prompt';
    let nextSteps: string[];
    const symptoms: string[] = [];

    // Identify specific symptoms
    if (answers['facial-spasms'] && !answers['facial-spasms'].includes('no-spasms')) {
      symptoms.push('Facial spasms');
    }
    if (answers['eyelid-involvement'] && !answers['eyelid-involvement'].includes('no-eyelid-symptoms')) {
      symptoms.push('Eyelid involvement');
    }
    if (answers['progression-pattern'] && !answers['progression-pattern'].includes('no-progression')) {
      symptoms.push('Progressive symptoms');
    }
    if (answers['unilateral-nature'] && answers['unilateral-nature'].includes('unilateral')) {
      symptoms.push('Unilateral pattern');
    }
    if (answers['trigger-factors'] && !answers['trigger-factors'].includes('no-triggers')) {
      symptoms.push('Trigger factors');
    }

    if (totalScore >= 18) {
      likelihood = 'high';
      urgency = 'prompt';
      recommendation = 'Your symptoms are highly suggestive of hemifacial spasm. The pattern and characteristics you describe are very consistent with this condition and warrant prompt medical evaluation.';
      nextSteps = [
        'Schedule appointment with neurologist within 1-2 weeks',
        'Request referral to movement disorder specialist',
        'Keep a detailed symptom diary with triggers',
        'Consider botulinum toxin treatment consultation'
      ];
    } else if (totalScore >= 10) {
      likelihood = 'moderate';
      urgency = 'prompt';
      recommendation = 'Your symptoms suggest possible hemifacial spasm that should be evaluated by a medical professional. Several features are consistent with this condition.';
      nextSteps = [
        'Schedule appointment with GP within 2-3 weeks',
        'Discuss symptoms and request neurologist referral',
        'Monitor symptom progression and triggers',
        'Consider keeping a symptom diary'
      ];
    } else if (totalScore >= 5) {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'You have some symptoms that could be related to various conditions. While hemifacial spasm is less likely, medical evaluation may still be beneficial.';
      nextSteps = [
        'Discuss symptoms with your GP at next routine visit',
        'Monitor symptoms for any changes or worsening',
        'Consider stress management techniques',
        'Seek medical attention if symptoms worsen'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Your symptoms are minimal and hemifacial spasm is unlikely. Continue with routine health maintenance and monitor for any new symptoms.';
      nextSteps = [
        'Continue routine health check-ups',
        'Be aware of hemifacial spasm warning signs',
        'Practice stress management and healthy lifestyle',
        'Seek medical attention if new symptoms develop'
      ];
    }

    return { totalScore, likelihood, recommendation, urgency, nextSteps, symptoms };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted text-foreground border-border';
      case 'moderate': return 'bg-info-light/30 text-foreground border-info/30';
      default: return 'bg-success-light/30 text-foreground border-success/30';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'prompt': return <AlertTriangle className="h-5 w-5 text-info" />;
      case 'urgent': return <Brain className="h-5 w-5 text-foreground" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn(
        "bg-background",
        deviceInfo.isMobile ? "py-12" : "py-20"
      )}>
        <div className="container max-w-5xl">
          <Card className={cn(
            "border-2 shadow-2xl bg-card/80 backdrop-blur-sm",
            getLikelihoodColor(results.likelihood)
          )}>
            <CardHeader className="text-center pb-8">
              <div className="flex items-center justify-center gap-3 mb-6">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className={cn(
                  "font-bold text-foreground leading-tight",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Hemifacial Spasm Assessment Results
                </CardTitle>
              </div>
              <CardDescription className={cn(
                "text-muted-foreground leading-relaxed",
                deviceInfo.isMobile ? "text-sm" : "text-base"
              )}>
                Based on your responses, here's your personalised assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score and Likelihood */}
              <div className="text-center p-6 bg-background/50 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div>
                    <div className="text-enhanced-heading text-3xl font-bold text-primary">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground">Assessment Score</div>
                  </div>
                  <div>
                    <Badge className={getLikelihoodColor(results.likelihood)}>
                      {results.likelihood.toUpperCase()} likelihood
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Identified Symptoms */}
              {results.symptoms.length > 0 && (
                <div className="bg-background rounded-lg p-6 border">
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    <Zap className="h-5 w-5 text-info" />
                    Identified Symptoms
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {results.symptoms.map((symptom, index) => (
                      <Badge key={index} variant="secondary" className="bg-info-light text-foreground">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recommendation
                </h3>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis. 
                      Many symptoms can have multiple causes. Always consult with qualified healthcare professionals for proper evaluation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg" >
                  <Brain className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg"  onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section
      className={cn(
        "bg-gradient-to-br from-primary/5 via-background to-primary/10",
        "border-y border-border/50",
        deviceInfo.isMobile ? "py-12" : "py-20"
      )}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-4xl">
        <div className="text-center mb-12">
          <h2
            id={`${assessmentId}-title`}
            className={cn(
              "font-bold text-foreground mb-6 leading-tight",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
            )}
          >
            Hemifacial Spasm Symptom Assessment
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto leading-relaxed",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Answer these questions to assess your symptoms and receive personalised guidance about when to seek medical attention
          </p>
        </div>

        {/* Progress */}
        <div className="mb-10">
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm font-semibold text-foreground">Progress</span>
            <span className="text-sm text-muted-foreground font-medium">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-3 bg-muted/50" />
        </div>

        {/* Question Card */}
        <Card className={cn(
          "bg-card/80 backdrop-blur-sm border-border/50",
          "shadow-xl hover:shadow-2xl transition-all duration-300"
        )}>
          <CardHeader className="pb-6">
            <CardTitle className={cn(
              "flex items-center gap-3 text-foreground",
              deviceInfo.isMobile ? "text-lg" : "text-xl"
            )}>
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center border-2 border-primary/20">
                <TrendingUp className="h-5 w-5 text-primary" />
              </div>
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className={cn(
              "font-semibold text-foreground leading-relaxed",
              deviceInfo.isMobile ? "text-base" : "text-lg"
            )}>
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className={cn(
                "text-muted-foreground leading-relaxed",
                deviceInfo.isMobile ? "text-sm" : "text-base"
              )}>
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default HemifacialSymptomAssessment;
