import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Left, ArrowRight, Brain, CheckCircle, Heart, TrendingUp, Zap } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'immediate';
  nextSteps: string[];
  symptoms: string[];
  ageGroup: 'infant' | 'child' | 'adult' | 'elderly';
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'age-group',
    question: 'What is the age group of the person being assessed?',
    description: 'Hydrocephalus symptoms vary significantly by age group',
    options: [
      { value: 'infant', label: 'Infant (0-18 months)', score: 0 },
      { value: 'child', label: 'Child (18 months - 12 years)', score: 0 },
      { value: 'adult', label: 'Adult (13-65 years)', score: 0 },
      { value: 'elderly', label: 'Elderly (over 65 years)', score: 0 }
    ]
  },
  {
    id: 'headaches',
    question: 'Are there headaches, particularly severe or morning headaches?',
    description: 'Headaches are a common symptom of increased intracranial pressure',
    options: [
      { value: 'severe-morning', label: 'Yes, severe headaches especially in the morning', score: 5 },
      { value: 'frequent-headaches', label: 'Yes, frequent headaches throughout the day', score: 4 },
      { value: 'occasional-headaches', label: 'Yes, occasional headaches', score: 2 },
      { value: 'no-headaches', label: 'No headaches', score: 0 }
    ]
  },
  {
    id: 'nausea-vomiting',
    question: 'Is there nausea or vomiting, especially in the morning?',
    description: 'Morning vomiting can indicate increased intracranial pressure',
    options: [
      { value: 'persistent-vomiting', label: 'Yes, persistent vomiting especially mornings', score: 5 },
      { value: 'frequent-nausea', label: 'Yes, frequent nausea and occasional vomiting', score: 3 },
      { value: 'mild-nausea', label: 'Yes, mild nausea occasionally', score: 1 },
      { value: 'no-nausea', label: 'No nausea or vomiting', score: 0 }
    ]
  },
  {
    id: 'vision-problems',
    question: 'Are there vision problems such as blurred or double vision?',
    description: 'Vision changes can result from increased pressure affecting the optic nerves',
    options: [
      { value: 'severe-vision-loss', label: 'Yes, significant vision loss or double vision', score: 4 },
      { value: 'blurred-vision', label: 'Yes, blurred vision or visual disturbances', score: 3 },
      { value: 'mild-vision-changes', label: 'Yes, mild vision changes', score: 2 },
      { value: 'no-vision-problems', label: 'No vision problems', score: 0 }
    ]
  },
  {
    id: 'balance-coordination',
    question: 'Are there balance problems, unsteady walking, or coordination issues?',
    description: 'Balance and coordination problems are common in hydrocephalus',
    options: [
      { value: 'severe-balance-issues', label: 'Yes, significant balance problems affecting daily activities', score: 4 },
      { value: 'moderate-unsteadiness', label: 'Yes, noticeable unsteadiness or coordination problems', score: 3 },
      { value: 'mild-balance-issues', label: 'Yes, mild balance or coordination concerns', score: 2 },
      { value: 'no-balance-issues', label: 'No balance or coordination problems', score: 0 }
    ]
  },
  {
    id: 'cognitive-changes',
    question: 'Are there memory problems, confusion, or personality changes?',
    description: 'Cognitive changes can indicate pressure effects on brain tissue',
    options: [
      { value: 'severe-cognitive-decline', label: 'Yes, significant memory loss or personality changes', score: 4 },
      { value: 'moderate-confusion', label: 'Yes, noticeable confusion or memory problems', score: 3 },
      { value: 'mild-cognitive-changes', label: 'Yes, mild memory or concentration issues', score: 2 },
      { value: 'no-cognitive-changes', label: 'No cognitive or personality changes', score: 0 }
    ]
  },
  {
    id: 'infant-specific',
    question: 'For infants: Is there rapid head growth, bulging fontanelle, or feeding difficulties?',
    description: 'Specific signs of hydrocephalus in infants with open skull sutures',
    options: [
      { value: 'multiple-infant-signs', label: 'Yes, multiple signs including rapid head growth', score: 6 },
      { value: 'bulging-fontanelle', label: 'Yes, bulging soft spot or feeding difficulties', score: 4 },
      { value: 'mild-infant-signs', label: 'Yes, some concerning signs', score: 2 },
      { value: 'no-infant-signs', label: 'No concerning infant signs or not applicable', score: 0 }
    ]
  }
];

export function HydrocephalusSymptomAssessment() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      if (questionId === 'age-group') return total; // Age group doesn't contribute to score
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    const ageGroup = answers['age-group'] as 'infant' | 'child' | 'adult' | 'elderly' || 'adult';

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'immediate';
    let nextSteps: string[];
    const symptoms: string[] = [];

    // Identify specific symptoms
    if (answers['headaches'] && !answers['headaches'].includes('no-headaches')) {
      symptoms.push('Headaches');
    }
    if (answers['nausea-vomiting'] && !answers['nausea-vomiting'].includes('no-nausea')) {
      symptoms.push('Nausea/vomiting');
    }
    if (answers['vision-problems'] && !answers['vision-problems'].includes('no-vision-problems')) {
      symptoms.push('Vision problems');
    }
    if (answers['balance-coordination'] && !answers['balance-coordination'].includes('no-balance-issues')) {
      symptoms.push('Balance/coordination issues');
    }
    if (answers['cognitive-changes'] && !answers['cognitive-changes'].includes('no-cognitive-changes')) {
      symptoms.push('Cognitive changes');
    }
    if (answers['infant-specific'] && !answers['infant-specific'].includes('no-infant-signs')) {
      symptoms.push('Infant-specific signs');
    }

    // Adjust scoring based on age group and specific high-risk symptoms
    let adjustedScore = totalScore;
    if (ageGroup === 'infant' && answers['infant-specific']?.includes('multiple-infant-signs')) {
      adjustedScore += 3; // Infant signs are particularly concerning
    }
    if (answers['nausea-vomiting']?.includes('persistent-vomiting')) {
      adjustedScore += 2; // Persistent vomiting is very concerning
    }

    if (adjustedScore >= 18) {
      likelihood = 'high';
      urgency = 'immediate';
      recommendation = 'Symptoms are highly suggestive of hydrocephalus and require immediate medical evaluation. The combination and severity of symptoms warrant urgent neurological assessment and brain imaging.';
      nextSteps = [
        'Go to emergency department immediately',
        'Request urgent neurology or neurosurgery consultation',
        'Arrange emergency brain imaging (CT or MRI)',
        'Do not delay - hydrocephalus can be life-threatening'
      ];
    } else if (adjustedScore >= 12) {
      likelihood = 'moderate';
      urgency = 'urgent';
      recommendation = 'Symptoms suggest possible hydrocephalus and require prompt medical evaluation. Several concerning features are present that need urgent assessment.';
      nextSteps = [
        'Contact GP or paediatrician within 24 hours',
        'Request urgent referral to neurology/neurosurgery',
        'Arrange brain imaging as soon as possible',
        'Monitor symptoms closely for any worsening'
      ];
    } else if (adjustedScore >= 6) {
      likelihood = 'low';
      urgency = 'urgent';
      recommendation = 'Some symptoms are present that could be related to hydrocephalus. Medical evaluation is recommended to rule out this condition.';
      nextSteps = [
        'Schedule appointment with GP within 1-2 weeks',
        'Discuss symptoms and request neurological assessment',
        'Consider brain imaging if symptoms persist',
        'Monitor for any new or worsening symptoms'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Current symptoms are minimal and hydrocephalus is unlikely. Continue routine health monitoring and be aware of warning signs.';
      nextSteps = [
        'Continue routine health check-ups',
        'Be aware of hydrocephalus warning signs',
        'Seek medical attention if new symptoms develop',
        'Maintain healthy lifestyle and development monitoring'
      ];
    }

    return { totalScore: adjustedScore, likelihood, recommendation, urgency, nextSteps, symptoms, ageGroup };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted-light text-foreground border-border/70';
      case 'moderate': return 'bg-info-light text-foreground border-info/30';
      default: return 'bg-success-light text-foreground border-success/30';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Brain className="h-5 w-5 text-info" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-16 section-background", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("medical-card border-2", getLikelihoodColor(results.likelihood))}>
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className="text-enhanced-heading text-2xl">Hydrocephalus Assessment Results</CardTitle>
              </div>
              <CardDescription className="text-enhanced-body">
                Based on your responses, here's your personalised assessment for {results.ageGroup} symptoms
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score and Likelihood */}
              <div className="text-center p-6 medical-card-inner rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-enhanced-heading text-3xl font-bold text-enhanced-accent">{results.totalScore}</div>
                    <div className="text-sm text-enhanced-muted">Assessment Score</div>
                  </div>
                  <div className="h-12 w-px bg-enhanced-border"></div>
                  <div className="text-center">
                    <Badge className={cn("text-sm font-medium", getLikelihoodColor(results.likelihood))}>
                      {results.likelihood.charAt(0).toUpperCase() + results.likelihood.slice(1)} Likelihood
                    </Badge>
                    <div className="text-sm text-enhanced-muted mt-1">Risk Level</div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm">
                  <TrendingUp className="h-4 w-4 text-enhanced-accent" />
                  <span className="text-enhanced-muted">
                    Based on {Object.keys(answers).length} assessment criteria
                  </span>
                </div>
              </div>

              {/* Age Group Specific Information */}
              <div className="medical-card-inner rounded-lg p-6 border border-enhanced-border">
                <h3 className="font-semibold mb-3 flex items-center gap-2 text-enhanced-heading">
                  <Heart className="h-5 w-5 text-enhanced-accent" />
                  Age Group: {results.ageGroup.charAt(0).toUpperCase() + results.ageGroup.slice(1)}
                </h3>
                <p className="text-sm text-enhanced-body">
                  {results.ageGroup === 'infant' && 
                    "In infants, hydrocephalus often presents with rapid head growth, bulging fontanelle, and feeding difficulties. Early detection is crucial for normal development."
                  }
                  {results.ageGroup === 'child' && 
                    "In children, symptoms typically include headaches, vomiting, and developmental concerns. School performance may be affected."
                  }
                  {results.ageGroup === 'adult' && 
                    "In adults, hydrocephalus usually presents with headaches, vision problems, and balance issues. It may be acquired due to injury or infection."
                  }
                  {results.ageGroup === 'elderly' && 
                    "In elderly patients, normal pressure hydrocephalus may present with the classic triad of gait problems, dementia, and urinary incontinence."
                  }
                </p>
              </div>

              {/* Identified Symptoms */}
              {results.symptoms.length > 0 && (
                <div className="medical-card-inner rounded-lg p-6 border border-enhanced-border">
                  <h3 className="font-semibold mb-3 flex items-center gap-2 text-enhanced-heading">
                    <Zap className="h-5 w-5 text-enhanced-accent" />
                    Identified Symptoms
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {results.symptoms.map((symptom, index) => (
                      <Badge key={index} variant="secondary" className="bg-enhanced-accent/10 text-enhanced-accent border border-enhanced-accent/20">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="medical-card-inner rounded-lg p-6 border border-enhanced-border">
                <h3 className="font-semibold mb-3 flex items-center gap-2 text-enhanced-heading">
                  <Activity className="h-5 w-5 text-enhanced-accent" />
                  Recommendation
                </h3>
                <p className="text-sm text-enhanced-body">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="medical-card-inner rounded-lg p-6 border border-enhanced-border">
                <h3 className="text-enhanced-heading font-semibold mb-3 flex items-center gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-enhanced-body text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info-light border border-info/30 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-enhanced-heading font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-enhanced-body text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis.
                      Hydrocephalus can be life-threatening and requires prompt medical evaluation. Always consult with qualified
                      healthcare professionals for proper evaluation and treatment.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button size="lg" >
                  <Brain className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg"  onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section
      className={cn("py-16 section-background", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2
            id={`${assessmentId}-title`}
            className={cn("font-bold mb-4 text-enhanced-heading", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}
          >
            Hydrocephalus Symptom Assessment
          </h2>
          <p className={cn("text-enhanced-body", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer these questions to assess symptoms and receive personalised guidance about when to seek medical attention
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-enhanced-heading">Progress</span>
            <span className="text-sm text-enhanced-muted">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="medical-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-enhanced-heading">
              <TrendingUp className="h-5 w-5 text-enhanced-accent" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium text-enhanced-body">
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-enhanced-muted">
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border border-enhanced-border hover:bg-enhanced-hover">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1 text-enhanced-body">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default HydrocephalusSymptomAssessment;
