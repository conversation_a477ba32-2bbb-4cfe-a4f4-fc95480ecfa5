import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON>tings, Target, TrendingUp, Zap } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ShuntSystem {
  name: string;
  description: string;
  components: string[];
  advantages: string[];
  disadvantages: string[];
  indications: string[];
  complications: string[];
}

interface ShuntSystemsComparisonProps {
  title: string;
  description: string;
  types: ShuntSystem[];
}

export function ShuntSystemsComparison({ 
  title, 
  description, 
  types 
}: ShuntSystemsComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [activeTab, setActiveTab] = useState('overview');

  const getShuntIcon = (name: string) => {
    if (name.includes('Programmable')) return Settings;
    if (name.includes('Fixed')) return Scissors;
    if (name.includes('Anti-Siphon')) return Activity;
    return Target;
  };

  const getShuntColor = (name: string) => {
    if (name.includes('Programmable')) return 'text-foreground bg-info-light border-info/30';
    if (name.includes('Fixed')) return 'text-foreground bg-success-light border-success/30';
    if (name.includes('Anti-Siphon')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getShuntType = (name: string) => {
    if (name.includes('Programmable')) return { label: 'Advanced', colour: 'badge-info' };
    if (name.includes('Fixed')) return { label: 'Standard', colour: 'badge-routine' };
    if (name.includes('Anti-Siphon')) return { label: 'Accessory', colour: 'badge-medical' };
    return { label: 'Other', colour: 'badge-info' };
  };

  const getComplexityScore = (system: ShuntSystem) => {
    // Calculate complexity based on components and features
    let score = system.components.length * 10;
    if (system.name.includes('Programmable')) score += 30;
    if (system.name.includes('Anti-Siphon')) score += 20;
    return Math.min(score, 100);
  };

  return (
    <section className={cn("section-spacing section-background-alt", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className={cn(
            "text-enhanced-heading font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-enhanced-body max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-2"
          )}>
            <TabsTrigger 
              value="overview"
              className={cn(
                "text-center",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Shunt System Overview
            </TabsTrigger>
            <TabsTrigger 
              value="comparison"
              className={cn(
                "text-center",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Detailed Comparison
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* System Complexity Comparison */}
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  System Complexity and Features
                </CardTitle>
                <CardDescription>
                  Comparison of sophistication and features across different shunt systems
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {types.map((system, index) => {
                    const complexityScore = getComplexityScore(system);
                    const shuntType = getShuntType(system.name);
                    
                    return (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{system.name}</span>
                            <Badge className={shuntType.colour}>
                              {shuntType.label}
                            </Badge>
                          </div>
                          <span className="text-sm font-medium">{complexityScore}% complexity</span>
                        </div>
                        <Progress value={complexityScore} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Quick Comparison Grid */}
            <div className="grid gap-6 md:grid-cols-3">
              {types.map((system, index) => {
                const Icon = getShuntIcon(system.name);
                const shuntType = getShuntType(system.name);
                
                return (
                  <Card key={index} className={cn("transition-all duration-200", getShuntColor(system.name))}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="p-2 rounded-full medical-card/80 border border-border/50">
                            <Icon className="h-5 w-5" />
                          </div>
                          <div>
                            <CardTitle className="text-enhanced-heading text-lg">{system.name}</CardTitle>
                          </div>
                        </div>
                        <Badge className={shuntType.colour}>
                          {shuntType.label}
                        </Badge>
                      </div>
                      <CardDescription className="text-sm">{system.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid gap-3 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Components:</span>
                            <span className="font-medium">{system.components.length} parts</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Advantages:</span>
                            <span className="font-medium">{system.advantages.length} benefits</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Complications:</span>
                            <span className="font-medium">{system.complications.length} types</span>
                          </div>
                        </div>
                        
                        <div className="content-spacing-sm">
                          <h4 className="text-enhanced-heading font-medium text-sm">Key Components:</h4>
                          <ul className="space-y-1">
                            {system.components.slice(0, 2).map((component, idx) => (
                              <li key={idx} className="text-enhanced-body text-xs flex items-start gap-2">
                                <CheckCircle className="h-3 w-3 text-success mt-0.5 flex-shrink-0" />
                                {component}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-6">
            {types.map((system, index) => {
              const Icon = getShuntIcon(system.name);
              const shuntType = getShuntType(system.name);
              
              return (
                <Card key={index} className="medical-card">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-enhanced-heading text-xl">{system.name}</CardTitle>
                          <CardDescription className="text-enhanced-muted">{system.description}</CardDescription>
                        </div>
                      </div>
                      <Badge className={shuntType.colour}>
                        {shuntType.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="content-spacing">
                    {/* Components */}
                    <div>
                      <h4 className="text-enhanced-heading font-semibold mb-3 flex items-center gap-2">
                        <Settings className="h-4 w-4 text-info" />
                        System Components
                      </h4>
                      <div className="grid gap-2 md:grid-cols-2">
                        {system.components.map((component, idx) => (
                          <div key={idx} className="flex items-start gap-2 p-2 bg-info-light rounded">
                            <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                            <span className="text-enhanced-body text-sm">{component}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Detailed Information */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-heading font-semibold mb-3 text-success">Advantages</h4>
                        <ul className="space-y-2">
                          {system.advantages.map((advantage, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-semibold mb-3 text-foreground">Disadvantages</h4>
                        <ul className="space-y-2">
                          {system.disadvantages.map((disadvantage, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{disadvantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-semibold mb-3 text-info">Indications</h4>
                        <ul className="space-y-2">
                          {system.indications.map((indication, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-heading font-semibold mb-3 text-info">Potential Complications</h4>
                        <ul className="space-y-2">
                          {system.complications.map((complication, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{complication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Clinical Notes */}
                    <div className="bg-info-light border border-info/30 rounded-lg p-4">
                      <h4 className="text-enhanced-heading font-semibold text-info mb-2">Clinical Considerations</h4>
                      <p className="text-enhanced-body text-sm text-info">
                        {system.name.includes('Programmable') && 
                          "Programmable valve shunts represent the latest advancement in hydrocephalus treatment. The ability to adjust pressure settings non-invasively allows for optimisation of drainage and can significantly reduce the need for revision surgeries. These systems are particularly beneficial for patients with normal pressure hydrocephalus or those who experience over-drainage complications."
                        }
                        {system.name.includes('Fixed') && 
                          "Fixed pressure valve shunts are the traditional and most widely used shunt systems. Their reliability and extensive clinical experience make them an excellent choice for most hydrocephalus cases. While they lack the adjustability of programmable systems, their simplicity and lower cost make them particularly suitable for paediatric cases and emergency situations."
                        }
                        {system.name.includes('Anti-Siphon') && 
                          "Anti-siphon devices are important accessories that help prevent over-drainage complications, particularly in tall patients or those who are very active. These devices work by counteracting the siphoning effect that can occur when patients are upright, providing more physiological drainage patterns and reducing symptoms like headaches and slit ventricle syndrome."
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
        </Tabs>

        {/* Shunt Selection Guide */}
        <div className="mt-12">
          <Card className="medical-card bg-info-light border border-info/30">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Shunt System Selection Guide
              </CardTitle>
              <CardDescription className="text-enhanced-body">
                Factors to consider when choosing the most appropriate shunt system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-4"
              )}>
                <div className="text-center p-4 medical-card-inner rounded-lg border border-enhanced-border">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-2">Patient Age</h4>
                  <p className="text-enhanced-muted text-sm">
                    Consider growth, activity level, and long-term needs
                  </p>
                </div>
                <div className="text-center p-4 medical-card-inner rounded-lg border border-enhanced-border">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-2">Hydrocephalus Type</h4>
                  <p className="text-enhanced-muted text-sm">
                    Communicating vs non-communicating, underlying cause
                  </p>
                </div>
                <div className="text-center p-4 medical-card-inner rounded-lg border border-enhanced-border">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-2">Previous History</h4>
                  <p className="text-enhanced-muted text-sm">
                    Prior shunt complications or revision history
                  </p>
                </div>
                <div className="text-center p-4 medical-card-inner rounded-lg border border-enhanced-border">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center font-bold mx-auto mb-3">
                    4
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-2">Lifestyle Factors</h4>
                  <p className="text-enhanced-muted text-sm">
                    Activity level, occupation, and personal preferences
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Shunt Maintenance */}
        <div className="mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-2 text-info">
                <Zap className="h-5 w-5" />
                Shunt System Maintenance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-enhanced-body text-info text-sm">
                All shunt systems require lifelong monitoring and maintenance. Regular follow-up appointments, 
                awareness of malfunction symptoms, and prompt medical attention for any concerns are essential 
                for optimal outcomes. Modern shunt systems are highly reliable, but patients and families should 
                be educated about warning signs and the importance of ongoing care.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card className="medical-card bg-primary/5 border border-primary/20">
            <CardContent className="pt-6">
              <h3 className="text-enhanced-heading font-semibold mb-2">Expert Hydrocephalus Treatment</h3>
              <p className="text-enhanced-body mb-4">
                Our neurosurgical team provides comprehensive evaluation and treatment for hydrocephalus.
                Personalised shunt selection and ongoing care for optimal long-term outcomes.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"} >
                Schedule Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default ShuntSystemsComparison;
