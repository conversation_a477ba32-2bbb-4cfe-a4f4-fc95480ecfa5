import { Activity, AlertTriangle, CheckCircle, Scissors, Shield, Star, Target, TrendingUp, XCircle } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string; size?: number | string; }>;
  description: string;
  effectiveness: number;
  timeToRelief: string;
  duration: string;
  cost: 'Low' | 'Medium' | 'High';
  invasiveness: 'Non-invasive' | 'Minimally invasive' | 'Invasive';
  pros: string[];
  cons: string[];
  scientificEvidence: string;
  successRate: string;
  suitableFor: string[];
  contraindications: string[];
}

interface MeralgiaComparisonSectionProps {
  className?: string;
}

const treatmentOptions: TreatmentOption[] = [
  {
    id: 'conservative',
    title: 'Conservative Management',
    icon: Shield,
    description: 'Non-invasive approach focusing on lifestyle modifications, ergonomic changes, and physical therapy',
    effectiveness: 75,
    timeToRelief: '4-12 weeks',
    duration: '6-12 months',
    cost: 'Low',
    invasiveness: 'Non-invasive',
    pros: [
      'No side effects or complications',
      'Addresses root causes',
      'Cost-effective approach',
      'Improves overall health',
      'Can prevent recurrence'
    ],
    cons: [
      'Slower symptom relief',
      'Requires patient compliance',
      'May not work for severe cases',
      'Lifestyle changes needed'
    ],
    scientificEvidence: 'Strong evidence (Level A) - Multiple RCTs show 70-80% success rate with conservative management',
    successRate: '70-80%',
    suitableFor: [
      'Mild to moderate symptoms',
      'Recent onset (<6 months)',
      'Motivated patients',
      'Identifiable risk factors'
    ],
    contraindications: [
      'Severe, debilitating symptoms',
      'Failed previous conservative treatment',
      'Urgent symptom relief needed'
    ]
  },
  {
    id: 'medication',
    title: 'Medication Therapy',
    icon: Pill,
    description: 'Pharmacological management using neuropathic pain medications and anti-inflammatory drugs',
    effectiveness: 65,
    timeToRelief: '1-4 weeks',
    duration: '3-6 months',
    cost: 'Medium',
    invasiveness: 'Non-invasive',
    pros: [
      'Rapid symptom relief',
      'Easy to implement',
      'Can be combined with other treatments',
      'Adjustable dosing',
      'Good for neuropathic pain'
    ],
    cons: [
      'Potential side effects',
      'Drug interactions possible',
      'May mask underlying problem',
      'Tolerance can develop',
      'Temporary solution'
    ],
    scientificEvidence: 'Moderate evidence (Level B) - Studies show 60-70% improvement in neuropathic pain symptoms',
    successRate: '60-70%',
    suitableFor: [
      'Moderate to severe pain',
      'Neuropathic pain characteristics',
      'Need for rapid relief',
      'Combined treatment approach'
    ],
    contraindications: [
      'Drug allergies',
      'Significant comorbidities',
      'Drug interactions',
      'Pregnancy (certain medications)'
    ]
  },
  {
    id: 'injection',
    title: 'Injection Therapy',
    icon: Target,
    description: 'Targeted corticosteroid injections near the lateral femoral cutaneous nerve for inflammation reduction',
    effectiveness: 85,
    timeToRelief: '1-7 days',
    duration: '3-12 months',
    cost: 'Medium',
    invasiveness: 'Minimally invasive',
    pros: [
      'High success rate',
      'Rapid symptom relief',
      'Targeted treatment',
      'Diagnostic and therapeutic',
      'Can be repeated if needed'
    ],
    cons: [
      'Temporary relief',
      'Injection risks',
      'May need multiple injections',
      'Cost of procedures',
      'Requires skilled practitioner'
    ],
    scientificEvidence: 'Strong evidence (Level A) - Multiple studies demonstrate 80-90% success rate with steroid injections',
    successRate: '80-90%',
    suitableFor: [
      'Failed conservative treatment',
      'Moderate to severe symptoms',
      'Need for rapid relief',
      'Diagnostic uncertainty'
    ],
    contraindications: [
      'Active infection',
      'Bleeding disorders',
      'Allergy to medications',
      'Uncontrolled diabetes'
    ]
  },
  {
    id: 'surgical',
    title: 'Surgical Intervention',
    icon: Scissors,
    description: 'Surgical decompression or neurectomy for refractory cases not responding to conservative treatment',
    effectiveness: 70,
    timeToRelief: '2-6 weeks',
    duration: 'Permanent',
    cost: 'High',
    invasiveness: 'Invasive',
    pros: [
      'Potential permanent solution',
      'Addresses anatomical problem',
      'Good for refractory cases',
      'Minimally invasive options available'
    ],
    cons: [
      'Surgical risks and complications',
      'Permanent numbness possible',
      'Recovery time required',
      'Higher cost',
      'Variable success rates'
    ],
    scientificEvidence: 'Limited evidence (Level C) - Case series show 60-80% success but limited long-term studies',
    successRate: '60-80%',
    suitableFor: [
      'Failed all conservative treatments',
      'Severe, debilitating symptoms',
      'Anatomical compression confirmed',
      'Good surgical candidates'
    ],
    contraindications: [
      'Poor surgical candidates',
      'Mild symptoms',
      'Recent onset',
      'Significant comorbidities'
    ]
  }
];

const MeralgiaComparisonSection: React.FC<MeralgiaComparisonSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>('conservative');

  const getEffectivenessColor = (effectiveness: number) => {
    if (effectiveness >= 80) return 'text-success';
    if (effectiveness >= 70) return 'text-info';
    if (effectiveness >= 60) return 'text-info';
    return 'text-muted-foreground';
  };

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'Low': return 'bg-success-light text-foreground border border-success/30';
      case 'Medium': return 'bg-info-light text-foreground border border-info/30';
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <TrendingUp className="w-4 h-4 mr-2" />
            Treatment Comparison
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Treatment Options Comparison
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive analysis of treatment modalities with scientific evidence, pros and cons, and success rates
          </p>
        </div>

        {/* Treatment Comparison Tabs */}
        <Tabs value={selectedTreatment} onValueChange={setSelectedTreatment} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {treatmentOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <TabsTrigger 
                  key={option.id} 
                  value={option.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {option.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Treatment Details */}
          {treatmentOptions.map((option) => (
            <TabsContent key={option.id} value={option.id} className="space-y-8">
              {/* Overview Card */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <option.icon className="w-6 h-6 text-primary" />
                    {option.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{option.description}</p>
                </CardHeader>
                <CardContent>
                  <div className={cn(
                    "grid gap-6",
                    deviceInfo.isMobile ? "grid-cols-2" : "grid-cols-2 md:grid-cols-4"
                  )}>
                    <div className="text-center">
                      <div className={cn(
                        "text-2xl font-bold mb-1",
                        getEffectivenessColor(option.effectiveness)
                      )}>
                        {option.effectiveness}%
                      </div>
                      <div className="text-enhanced-caption">Effectiveness</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground mb-1">{option.timeToRelief}</div>
                      <div className="text-enhanced-caption">Time to Relief</div>
                    </div>
                    <div className="text-center">
                      <Badge className={getCostColor(option.cost)}>
                        {option.cost} Cost
                      </Badge>
                      <div className="text-enhanced-caption mt-1">Investment</div>
                    </div>
                    <div className="text-center">
                      <div className="text-enhanced-body font-semibold mb-1">{option.invasiveness}</div>
                      <div className="text-enhanced-caption">Approach</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Pros and Cons */}
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
              )}>
                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-success" />
                      Advantages
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {option.pros.map((pro, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                          <span className="text-enhanced-body">{pro}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <XCircle className="w-5 h-5 text-muted-foreground" />
                      Considerations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {option.cons.map((con, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <XCircle className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                          <span className="text-enhanced-body">{con}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Scientific Evidence and Suitability */}
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
              )}>
                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <Star className="w-5 h-5 text-primary" />
                      Scientific Evidence
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="w-4 h-4 text-success" />
                        <span className="text-enhanced-subheading font-semibold">Success Rate: {option.successRate}</span>
                      </div>
                      <p className="text-enhanced-body text-sm">{option.scientificEvidence}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="medical-card">
                  <CardHeader>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <Target className="w-5 h-5 text-info" />
                      Best Suited For
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {option.suitableFor.map((criteria, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{criteria}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Contraindications */}
              {option.contraindications.length > 0 && (
                <Card className="medical-card border-l-4 border-l-muted">
                  <CardHeader>
                    <CardTitle className="text-enhanced-heading flex items-center gap-3">
                      <AlertTriangle className="w-5 h-5 text-foreground" />
                      Contraindications & Precautions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {option.contraindications.map((contraindication, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{contraindication}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Treatment Decision Guide */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Activity className="w-5 h-5 text-primary" />
              Treatment Decision Framework
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">First-Line Treatment</h4>
                <p className="text-enhanced-body text-sm">Conservative management for 6-12 weeks with lifestyle modifications and physical therapy</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Target className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Second-Line Treatment</h4>
                <p className="text-enhanced-body text-sm">Injection therapy or medications if conservative treatment fails or rapid relief needed</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Scissors className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Last Resort</h4>
                <p className="text-enhanced-body text-sm">Surgical intervention only for severe, refractory cases after failed conservative treatment</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default MeralgiaComparisonSection;
