import { Activity, AlertTriangle, Brain, Eye, Footprints, Heart, MapPin, Shield, Target, Zap } from 'lucide-react';
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface _AnatomyPoint {
  id: string;
  title: string;
  description: string;
  clinicalSignificance: string;
  location: { x: number; y: number };
}

interface PeronealAnatomySectionProps {
  className?: string;
}

const anatomyData = {
  title: "Common Peroneal Nerve Anatomy and Vulnerability",
  subtitle: "Understanding the anatomical structures involved in peroneal nerve palsy and foot drop",
  
  nervePathway: [
    "Originates from the sciatic nerve in the popliteal fossa",
    "Travels laterally around the fibular head (most vulnerable point)",
    "Divides into deep and superficial peroneal nerves",
    "Deep branch controls foot dorsiflexion and toe extension",
    "Superficial branch provides sensation to foot dorsum",
    "Supplies muscles essential for foot clearance during walking"
  ],

  vulnerabilityFactors: [
    "Superficial location at the fibular head with minimal soft tissue protection",
    "Fixed position against bone making it susceptible to compression",
    "Narrow fibro-osseous tunnel where nerve can be trapped",
    "Limited blood supply in the region around fibular head",
    "Anatomical variations that may increase compression risk",
    "Proximity to surgical sites and trauma-prone areas"
  ],

  functionalAnatomy: [
    "Deep peroneal nerve: Controls tibialis anterior, extensor digitorum longus",
    "Superficial peroneal nerve: Controls peroneus longus and brevis",
    "Motor function: Essential for foot dorsiflexion and eversion",
    "Sensory function: Provides feeling to foot dorsum and web spaces",
    "Functional importance: Critical for normal walking and foot clearance",
    "Compensation patterns: Other muscles may partially compensate for weakness"
  ],

  anatomyPoints: [
    {
      id: 'fibular-head',
      title: 'Fibular Head',
      description: 'Bony prominence where the common peroneal nerve is most vulnerable',
      clinicalSignificance: 'Primary site of nerve compression and injury in peroneal nerve palsy',
      location: { x: 30, y: 45 }
    },
    {
      id: 'common-peroneal-nerve',
      title: 'Common Peroneal Nerve',
      description: 'Main nerve trunk before division into deep and superficial branches',
      clinicalSignificance: 'Injury at this level causes both motor and sensory deficits',
      location: { x: 35, y: 55 }
    },
    {
      id: 'deep-peroneal-nerve',
      title: 'Deep Peroneal Nerve',
      description: 'Motor branch controlling foot dorsiflexion and toe extension',
      clinicalSignificance: 'Damage causes foot drop and inability to lift toes',
      location: { x: 45, y: 70 }
    },
    {
      id: 'superficial-peroneal-nerve',
      title: 'Superficial Peroneal Nerve',
      description: 'Branch providing sensation to foot dorsum and controlling foot eversion',
      clinicalSignificance: 'Injury causes numbness on top of foot and eversion weakness',
      location: { x: 55, y: 75 }
    },
    {
      id: 'anterior-compartment',
      title: 'Anterior Compartment Muscles',
      description: 'Muscles controlled by deep peroneal nerve including tibialis anterior',
      clinicalSignificance: 'Weakness in these muscles leads to foot drop and gait abnormalities',
      location: { x: 40, y: 85 }
    }
  ]
};

const PeronealAnatomySection: React.FC<PeronealAnatomySectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Brain className="w-4 h-4 mr-2" />
            Nerve Anatomy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {anatomyData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {anatomyData.subtitle}
          </p>
        </div>

        {/* Anatomy Tabs */}
        <Tabs defaultValue="pathway" className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-3 h-14"
          )}>
            <TabsTrigger 
              value="pathway"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <Zap className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Nerve Pathway</span>
            </TabsTrigger>
            <TabsTrigger 
              value="vulnerability"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <AlertTriangle className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Vulnerability</span>
            </TabsTrigger>
            <TabsTrigger 
              value="function"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <Activity className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Function</span>
            </TabsTrigger>
          </TabsList>

          {/* Nerve Pathway Tab */}
          <TabsContent value="pathway" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Zap className="w-5 h-5 text-primary" />
                  Common Peroneal Nerve Pathway
                </CardTitle>
                <p className="text-enhanced-body">
                  The common peroneal nerve follows a specific anatomical course that makes it vulnerable 
                  to injury, particularly at the fibular head.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.nervePathway.map((step, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                        <span className="text-primary font-semibold text-sm">{index + 1}</span>
                      </div>
                      <p className="text-enhanced-body">{step}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vulnerability Tab */}
          <TabsContent value="vulnerability" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-foreground" />
                  Why the Peroneal Nerve is Vulnerable
                </CardTitle>
                <p className="text-enhanced-body">
                  Several anatomical factors make the common peroneal nerve particularly susceptible to injury and compression.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.vulnerabilityFactors.map((factor, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-foreground mt-0.5 flex-shrink-0" />
                      <p className="text-enhanced-body">{factor}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Function Tab */}
          <TabsContent value="function" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Activity className="w-5 h-5 text-info" />
                  Functional Anatomy and Clinical Impact
                </CardTitle>
                <p className="text-enhanced-body">
                  Understanding the functional anatomy helps explain the symptoms and impact of peroneal nerve injury.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.functionalAnatomy.map((detail, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <Activity className="w-5 h-5 text-info mt-0.5 flex-shrink-0" />
                      <p className="text-enhanced-body">{detail}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Interactive Anatomy Diagram */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Eye className="w-5 h-5 text-primary" />
              Interactive Anatomy Visualisation
            </CardTitle>
            <p className="text-enhanced-body">
              Click on the anatomical points below to learn about key structures involved in peroneal nerve palsy.
            </p>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-8",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"
            )}>
              {/* Anatomy Image with Interactive Points */}
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                  alt="Common peroneal nerve anatomy and fibular head"
                  className="w-full h-auto rounded-lg"
                  fallbackSrc="https://images.pexels.com/photos/7659564/pexels-photo-7659564.jpeg?auto=compress&cs=tinysrgb&w=600"
                />
                
                {/* Interactive Points */}
                {anatomyData.anatomyPoints.map((point) => (
                  <Button
                    key={point.id}
                    onClick={() => setSelectedPoint(selectedPoint === point.id ? null : point.id)}
                    className={cn(
                      "absolute w-4 h-4 rounded-full border-2 transition-all duration-200",
                      selectedPoint === point.id
                        ? "bg-primary border-primary scale-125 shadow-lg"
                        : "bg-background border-primary hover:scale-110 hover:bg-primary/10"
                    )}
                    style={{
                      left: `${point.location.x}%`,
                      top: `${point.location.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    aria-label={`View information about ${point.title}`}
                  />
                ))}
              </div>

              {/* Anatomy Point Details */}
              <div className="space-y-4">
                {selectedPoint ? (
                  (() => {
                    const point = anatomyData.anatomyPoints.find(p => p.id === selectedPoint);
                    return point ? (
                      <Card className="medical-card border-l-4 border-l-primary">
                        <CardHeader>
                          <CardTitle className="text-enhanced-subheading">{point.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Description:</h4>
                            <p className="text-enhanced-body text-sm">{point.description}</p>
                          </div>
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Clinical Significance:</h4>
                            <p className="text-enhanced-body text-sm">{point.clinicalSignificance}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()
                ) : (
                  <Card className="medical-card">
                    <CardContent className="text-center py-8">
                      <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-enhanced-body">
                        Click on the anatomical points in the image to learn about key structures involved in peroneal nerve palsy.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Clinical Correlation */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Shield className="w-5 h-5 text-success" />
              Clinical-Anatomical Correlation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4">
                  <Footprints className="w-8 h-8 text-primary mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Foot Drop Mechanism</h4>
                <p className="text-enhanced-body text-sm">
                  Nerve anatomy explains why injury leads to inability to lift the foot during walking
                </p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Target className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Injury Patterns</h4>
                <p className="text-enhanced-body text-sm">
                  Understanding anatomy helps predict which functions will be affected by injury location
                </p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Heart className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Recovery Potential</h4>
                <p className="text-enhanced-body text-sm">
                  Anatomical knowledge guides treatment strategies and recovery expectations
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PeronealAnatomySection;
