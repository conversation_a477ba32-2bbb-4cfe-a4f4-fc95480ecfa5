import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Award, CheckCircle, Clock, Dumbbell, Heart, Play, RotateCcw, Shield, Target, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  duration: string;
  frequency: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  benefits: string[];
  precautions: string[];
  progressions: string[];
}

interface ExerciseCategory {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string; size?: number | string; }>;
  description: string;
  exercises: Exercise[];
}

interface PeronealExerciseSectionProps {
  className?: string;
}

const exerciseCategories: ExerciseCategory[] = [
  {
    id: 'strengthening',
    title: 'Strengthening Exercises',
    icon: Dumbbell,
    description: 'Targeted exercises to strengthen remaining muscles and improve compensation',
    exercises: [
      {
        id: 'ankle-dorsiflexion',
        name: 'Ankle Dorsiflexion Strengthening',
        description: 'Strengthen the muscles that lift the foot, working around nerve damage',
        instructions: [
          'Sit in a chair with feet flat on the floor',
          'Lift your toes and front of foot as high as possible',
          'Keep your heel on the ground throughout',
          'Hold the position for 5 seconds',
          'Slowly lower back to starting position',
          'Focus on controlled, deliberate movements'
        ],
        duration: '10-15 minutes',
        frequency: '3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Strengthens remaining dorsiflexor muscles',
          'Improves active range of motion',
          'Enhances muscle endurance',
          'Supports functional movement patterns'
        ],
        precautions: [
          'Stop if pain or cramping occurs',
          'Start gently and progress gradually',
          'Don\'t force movements beyond comfortable range'
        ],
        progressions: [
          'Add resistance band for increased difficulty',
          'Perform single-leg exercises',
          'Increase hold time to 10 seconds',
          'Add weight or resistance'
        ]
      },
      {
        id: 'hip-strengthening',
        name: 'Hip Flexor and Abductor Strengthening',
        description: 'Strengthen hip muscles to compensate for foot drop during walking',
        instructions: [
          'Lie on your back with legs straight',
          'Lift the affected leg straight up, leading with the hip',
          'Keep the knee straight and foot relaxed',
          'Hold for 5 seconds at the top',
          'Lower slowly and controlled',
          'Repeat with side-lying hip abduction'
        ],
        duration: '10-15 minutes',
        frequency: '2 times daily',
        difficulty: 'Intermediate',
        benefits: [
          'Improves compensatory hip strategies',
          'Enhances overall leg strength',
          'Supports better walking patterns',
          'Reduces fall risk'
        ],
        precautions: [
          'Avoid excessive hip flexion',
          'Stop if back pain develops',
          'Use proper form over speed'
        ],
        progressions: [
          'Add ankle weights',
          'Increase repetitions gradually',
          'Perform standing hip exercises',
          'Add functional movement patterns'
        ]
      }
    ]
  },
  {
    id: 'gait-training',
    title: 'Gait Training',
    icon: Footprints,
    description: 'Specialised walking exercises to improve safety and efficiency',
    exercises: [
      {
        id: 'high-step-marching',
        name: 'High-Step Marching',
        description: 'Practice the exaggerated stepping pattern needed for safe walking',
        instructions: [
          'Stand next to a wall or rail for support',
          'Lift the affected knee high while lifting the foot',
          'Aim to bring knee to waist level if possible',
          'Hold for 2-3 seconds',
          'Lower slowly with control',
          'Focus on clearing the foot from the ground'
        ],
        duration: '5-10 minutes',
        frequency: '3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Teaches compensatory gait pattern',
          'Improves hip flexor strength',
          'Enhances balance and coordination',
          'Reduces tripping risk'
        ],
        precautions: [
          'Always use support initially',
          'Start with small movements',
          'Stop if dizziness occurs'
        ],
        progressions: [
          'March in place without support',
          'Add arm swinging',
          'Increase marching speed',
          'Practice on different surfaces'
        ]
      },
      {
        id: 'heel-toe-walking',
        name: 'Heel-to-Toe Walking',
        description: 'Improve balance and coordination for safer walking',
        instructions: [
          'Stand at the end of a hallway or use a line on the floor',
          'Place one foot directly in front of the other',
          'Touch heel to toe with each step',
          'Use wall support if needed initially',
          'Focus on lifting the affected foot properly',
          'Take slow, deliberate steps'
        ],
        duration: '10-15 minutes',
        frequency: '2 times daily',
        difficulty: 'Intermediate',
        benefits: [
          'Improves dynamic balance',
          'Enhances proprioception',
          'Builds confidence in walking',
          'Strengthens stabilising muscles'
        ],
        precautions: [
          'Have support available',
          'Start with shorter distances',
          'Stop if balance becomes compromised'
        ],
        progressions: [
          'Walk without wall support',
          'Close eyes briefly during walking',
          'Walk on uneven surfaces',
          'Add head movements while walking'
        ]
      }
    ]
  },
  {
    id: 'balance-coordination',
    title: 'Balance & Coordination',
    icon: Scale,
    description: 'Exercises to improve stability and reduce fall risk',
    exercises: [
      {
        id: 'single-leg-standing',
        name: 'Single-Leg Standing',
        description: 'Improve balance and stability on the unaffected leg',
        instructions: [
          'Stand next to a wall or stable surface',
          'Lift the affected leg slightly off the ground',
          'Balance on the unaffected leg',
          'Hold for 10-30 seconds initially',
          'Use wall support as needed',
          'Focus on maintaining upright posture'
        ],
        duration: '5-10 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Intermediate',
        benefits: [
          'Improves single-leg balance',
          'Strengthens ankle stabilisers',
          'Enhances proprioception',
          'Builds confidence in standing'
        ],
        precautions: [
          'Always have support nearby',
          'Start with shorter hold times',
          'Stop if significant wobbling occurs'
        ],
        progressions: [
          'Increase hold time gradually',
          'Close eyes briefly',
          'Stand on foam pad',
          'Add arm movements'
        ]
      }
    ]
  },
  {
    id: 'functional-training',
    title: 'Functional Training',
    icon: Target,
    description: 'Real-world activities to improve daily function and independence',
    exercises: [
      {
        id: 'stair-climbing',
        name: 'Stair Climbing Practice',
        description: 'Safe techniques for navigating stairs with foot drop',
        instructions: [
          'Start with a single step or low platform',
          'Use handrails on both sides if available',
          'Lead with the unaffected leg going up',
          'Lead with the affected leg going down',
          'Lift the affected leg high to clear each step',
          'Take your time and don\'t rush'
        ],
        duration: '10-15 minutes',
        frequency: 'Daily',
        difficulty: 'Advanced',
        benefits: [
          'Improves stair navigation safety',
          'Builds confidence in daily activities',
          'Strengthens leg muscles functionally',
          'Enhances overall mobility'
        ],
        precautions: [
          'Always use handrails',
          'Start with low steps',
          'Have someone nearby for safety',
          'Stop if fatigue sets in'
        ],
        progressions: [
          'Increase number of steps',
          'Practice with different step heights',
          'Carry light objects while climbing',
          'Practice in different environments'
        ]
      }
    ]
  }
];

const PeronealExerciseSection: React.FC<PeronealExerciseSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('strengthening');

  const getDifficultyColour = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-success-light text-foreground border border-success/30';
      case 'Intermediate': return 'bg-info-light text-foreground border border-info/30';
      case 'Advanced': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Dumbbell className="w-4 h-4 mr-2" />
            Exercise & Therapy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Exercise and Rehabilitation Guide
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive exercise program designed to maximise function, improve safety, 
            and enhance quality of life for individuals with peroneal nerve palsy
          </p>
        </div>

        {/* Exercise Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {exerciseCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {exerciseCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Exercises */}
              <div className="space-y-8">
                {category.exercises.map((exercise) => (
                  <Card key={exercise.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{exercise.name}</CardTitle>
                        <Badge className={getDifficultyColour(exercise.difficulty)}>
                          {exercise.difficulty}
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{exercise.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Exercise Details */}
                      <div className={cn(
                        "grid gap-4",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                          <Clock className="w-5 h-5 text-info mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Duration</h5>
                          <p className="text-enhanced-body text-sm">{exercise.duration}</p>
                        </div>
                        <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                          <RotateCcw className="w-5 h-5 text-success mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Frequency</h5>
                          <p className="text-enhanced-body text-sm">{exercise.frequency}</p>
                        </div>
                        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                          <Target className="w-5 h-5 text-primary mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Level</h5>
                          <p className="text-enhanced-body text-sm">{exercise.difficulty}</p>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <Play className="w-4 h-4 text-primary" />
                          Step-by-Step Instructions
                        </h4>
                        <ol className="space-y-2">
                          {exercise.instructions.map((instruction, index) => (
                            <li key={index} className="flex items-start gap-3">
                              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                                <span className="text-primary font-semibold text-xs">{index + 1}</span>
                              </div>
                              <span className="text-enhanced-body text-sm">{instruction}</span>
                            </li>
                          ))}
                        </ol>
                      </div>

                      {/* Benefits and Precautions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Benefits
                          </h4>
                          <ul className="space-y-2">
                            {exercise.benefits.map((benefit, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Precautions
                          </h4>
                          <ul className="space-y-2">
                            {exercise.precautions.map((precaution, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{precaution}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Progressions */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-info" />
                          Exercise Progressions
                        </h4>
                        <ul className="space-y-2">
                          {exercise.progressions.map((progression, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{progression}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Exercise Program Guidelines */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Award className="w-5 h-5 text-primary" />
              Exercise Program Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Safety First</h4>
                <p className="text-enhanced-body text-sm">Always prioritise safety and use support when needed, especially during balance exercises</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <TrendingUp className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Gradual Progression</h4>
                <p className="text-enhanced-body text-sm">Start slowly and progress gradually to avoid fatigue and maintain motivation</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Consistency Matters</h4>
                <p className="text-enhanced-body text-sm">Regular daily exercise is more beneficial than occasional intensive sessions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PeronealExerciseSection;
