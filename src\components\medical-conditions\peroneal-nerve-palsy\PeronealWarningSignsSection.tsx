import { Activity, AlertTriangle, Clock, Eye, Heart, Phone, Shield, Target } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface _WarningSign {
  id: string;
  sign: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'concerning';
  action: string;
  timeframe: string;
}

interface _ProgressionStage {
  stage: string;
  title: string;
  description: string;
  symptoms: string[];
  reversibility: 'reversible' | 'partially-reversible' | 'permanent';
  timeframe: string;
}

interface PeronealWarningSignsSectionProps {
  className?: string;
}

const warningSignsData = {
  title: "Warning Signs and When to Seek Help",
  subtitle: "Recognise concerning symptoms and understand when immediate medical attention is needed for peroneal nerve palsy",
  
  warningSignsByUrgency: {
    immediate: [
      {
        id: 'sudden-weakness',
        sign: 'Sudden Complete Foot Drop',
        description: 'Sudden onset of complete inability to lift the foot or toes',
        urgency: 'immediate' as const,
        action: 'Seek emergency medical care immediately',
        timeframe: 'Within hours'
      },
      {
        id: 'bilateral-weakness',
        sign: 'Weakness in Both Legs',
        description: 'Foot drop or weakness affecting both legs simultaneously',
        urgency: 'immediate' as const,
        action: 'Call emergency services (000)',
        timeframe: 'Immediately'
      },
      {
        id: 'bowel-bladder',
        sign: 'Bowel or Bladder Problems',
        description: 'Loss of bowel or bladder control with leg weakness',
        urgency: 'immediate' as const,
        action: 'Emergency department evaluation',
        timeframe: 'Immediately'
      }
    ],
    urgent: [
      {
        id: 'progressive-weakness',
        sign: 'Progressive Worsening',
        description: 'Gradual worsening of foot drop or development of new weakness',
        urgency: 'urgent' as const,
        action: 'Contact healthcare provider within 24-48 hours',
        timeframe: 'Within 1-2 days'
      },
      {
        id: 'severe-pain',
        sign: 'Severe Leg or Back Pain',
        description: 'Intense pain in the leg, knee, or back associated with foot drop',
        urgency: 'urgent' as const,
        action: 'Schedule urgent medical consultation',
        timeframe: 'Within 2-3 days'
      },
      {
        id: 'infection-signs',
        sign: 'Signs of Infection',
        description: 'Fever, redness, or swelling around knee or leg with weakness',
        urgency: 'urgent' as const,
        action: 'Medical evaluation needed',
        timeframe: 'Within 24 hours'
      },
      {
        id: 'frequent-falls',
        sign: 'Frequent Falls or Near-Falls',
        description: 'Multiple falls or near-falls due to foot drop',
        urgency: 'urgent' as const,
        action: 'Urgent safety assessment needed',
        timeframe: 'Within 1-2 days'
      }
    ],
    concerning: [
      {
        id: 'no-improvement',
        sign: 'No Improvement After 3 Months',
        description: 'No signs of recovery or improvement after 3 months of appropriate treatment',
        urgency: 'concerning' as const,
        action: 'Review treatment plan with specialist',
        timeframe: 'Within 2 weeks'
      },
      {
        id: 'functional-decline',
        sign: 'Significant Functional Impact',
        description: 'Foot drop significantly affecting work, daily activities, or quality of life',
        urgency: 'concerning' as const,
        action: 'Consider specialist referral',
        timeframe: 'Within 2-4 weeks'
      },
      {
        id: 'skin-problems',
        sign: 'Skin Breakdown or Injury',
        description: 'Repeated skin injuries or pressure sores from dragging foot',
        urgency: 'concerning' as const,
        action: 'Medical evaluation recommended',
        timeframe: 'Within 1-2 weeks'
      },
      {
        id: 'psychological-impact',
        sign: 'Significant Psychological Impact',
        description: 'Depression, anxiety, or social withdrawal due to condition',
        urgency: 'concerning' as const,
        action: 'Comprehensive care evaluation',
        timeframe: 'Within 2-3 weeks'
      }
    ]
  },

  progressionStages: [
    {
      stage: 'Acute',
      title: 'Acute Stage (0-6 weeks)',
      description: 'Initial period following nerve injury with potential for recovery',
      symptoms: [
        'Sudden or gradual onset of foot drop',
        'Difficulty lifting foot and toes',
        'Possible numbness on top of foot',
        'Compensatory high-stepping gait'
      ],
      reversibility: 'reversible' as const,
      timeframe: '0-6 weeks'
    },
    {
      stage: 'Subacute',
      title: 'Subacute Stage (6 weeks - 6 months)',
      description: 'Period of potential nerve regeneration and functional adaptation',
      symptoms: [
        'Persistent foot drop with possible slight improvement',
        'Development of compensatory movement patterns',
        'Possible muscle atrophy beginning',
        'Adaptation to assistive devices'
      ],
      reversibility: 'partially-reversible' as const,
      timeframe: '6 weeks - 6 months'
    },
    {
      stage: 'Chronic',
      title: 'Chronic Stage (>6 months)',
      description: 'Established condition with focus on functional adaptation',
      symptoms: [
        'Persistent foot drop with minimal natural recovery',
        'Muscle atrophy in affected muscles',
        'Well-established compensatory patterns',
        'Dependence on assistive devices for function'
      ],
      reversibility: 'permanent' as const,
      timeframe: '>6 months'
    }
  ],

  whenToSeekHelp: {
    immediate: [
      'Sudden onset of complete foot drop',
      'Weakness affecting both legs',
      'Loss of bowel or bladder control',
      'Severe pain with neurological symptoms'
    ],
    urgent: [
      'Progressive worsening of symptoms over days',
      'Frequent falls or significant safety concerns',
      'Signs of infection or inflammation',
      'Severe pain interfering with daily activities'
    ],
    routine: [
      'Gradual onset of mild foot drop',
      'Questions about treatment options',
      'Need for assistive device assessment',
      'Concerns about long-term prognosis'
    ]
  }
};

const PeronealWarningSignsSection: React.FC<PeronealWarningSignsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="emergency" className="mb-6">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Warning Signs
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {warningSignsData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {warningSignsData.subtitle}
          </p>
        </div>

        {/* Emergency Contact Card */}
        <Card className="medical-card border-l-4 border-l-primary mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Phone className="w-5 h-5 text-primary" />
              Emergency Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <Phone className="w-8 h-8 text-foreground mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Emergency</h4>
                <p className="text-enhanced-body font-bold">000</p>
                <p className="text-enhanced-caption">Severe symptoms</p>
              </div>
              <div className="text-center">
                <Clock className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Urgent Care</h4>
                <p className="text-enhanced-body font-bold">(02) 9999 0000</p>
                <p className="text-enhanced-caption">Same-day appointment</p>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Clinic</h4>
                <p className="text-enhanced-body font-bold">(02) 8888 0000</p>
                <p className="text-enhanced-caption">Regular consultation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Warning Signs by Urgency */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Warning Signs by Urgency Level
          </h3>
          
          <div className="space-y-8">
            {Object.entries(warningSignsData.warningSignsByUrgency).map(([urgency, signs]) => (
              <Card key={urgency} className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-foreground" />
                    {urgency.charAt(0).toUpperCase() + urgency.slice(1)} Attention Required
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {signs.map((sign) => (
                      <div key={sign.id} className="border border-border/50 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="text-enhanced-subheading font-semibold">{sign.sign}</h4>
                          <Badge variant={sign.urgency === 'immediate' ? 'emergency' : sign.urgency === 'urgent' ? 'urgent' : 'routine'}>
                            {sign.urgency}
                          </Badge>
                        </div>
                        <p className="text-enhanced-body mb-3">{sign.description}</p>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <span className="text-enhanced-caption font-medium">Action Required: </span>
                            <span className="text-enhanced-body text-sm">{sign.action}</span>
                          </div>
                          <div>
                            <span className="text-enhanced-caption font-medium">Timeframe: </span>
                            <span className="text-enhanced-body text-sm">{sign.timeframe}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recovery Stages */}
        <div className="mb-16">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            Recovery Stages and Timeline
          </h3>
          
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
          )}>
            {warningSignsData.progressionStages.map((stage, _index) => (
              <Card key={stage.stage} className="medical-card h-full">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-enhanced-heading">{stage.title}</CardTitle>
                    <Badge variant={stage.reversibility === 'reversible' ? 'routine' : stage.reversibility === 'partially-reversible' ? 'urgent' : 'emergency'}>
                      {stage.reversibility.replace('-', ' ')}
                    </Badge>
                  </div>
                  <p className="text-enhanced-body text-sm">{stage.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-enhanced-caption font-medium mb-2">Typical Symptoms:</h4>
                    <ul className="space-y-1">
                      {stage.symptoms.map((symptom, symptomIndex) => (
                        <li key={symptomIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{symptom}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="pt-2 border-t border-border/50">
                    <span className="text-enhanced-caption font-medium">Duration: </span>
                    <span className="text-enhanced-body text-sm">{stage.timeframe}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* When to Seek Help */}
        <div className="mb-12">
          <h3 className={cn(
            "font-bold text-foreground mb-8 text-center",
            deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
          )}>
            When to Seek Medical Help
          </h3>
          
          <div className={cn(
            "grid gap-8",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
          )}>
            <Card className="medical-card border-l-4 border-l-muted">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Phone className="w-5 h-5 text-foreground" />
                  Immediate Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.immediate.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="medical-card border-l-4 border-l-info">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Clock className="w-5 h-5 text-info" />
                  Urgent Care
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {warningSignsData.whenToSeekHelp.urgent.map((item, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Clock className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                      <span className="text-enhanced-body text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Key Takeaways */}
        <Card className="medical-card">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Target className="w-5 h-5 text-primary" />
              Key Takeaways
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Eye className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Early Recognition</h4>
                <p className="text-enhanced-body text-sm">Early identification and treatment optimise recovery potential and functional outcomes</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Activity className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Safety Priority</h4>
                <p className="text-enhanced-body text-sm">Immediate safety measures and fall prevention are crucial while seeking treatment</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Comprehensive Care</h4>
                <p className="text-enhanced-body text-sm">Address both physical and psychological aspects of living with foot drop</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PeronealWarningSignsSection;
