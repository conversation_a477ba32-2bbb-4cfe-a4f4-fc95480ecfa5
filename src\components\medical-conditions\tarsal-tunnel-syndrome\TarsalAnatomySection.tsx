import { Activity, AlertTriangle, Brain, Eye, Footprints, Heart, MapPin, Shield, Target, Zap } from 'lucide-react';
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface _AnatomyPoint {
  id: string;
  title: string;
  description: string;
  clinicalSignificance: string;
  location: { x: number; y: number };
}

interface TarsalAnatomySectionProps {
  className?: string;
}

const anatomyData = {
  title: "Posterior Tibial Nerve and Tarsal Tunnel Anatomy",
  subtitle: "Understanding the anatomical structures involved in tarsal tunnel syndrome",
  
  nervePathway: [
    "Originates from the tibial nerve in the popliteal fossa",
    "Travels down the posterior leg between calf muscles",
    "Passes behind the medial malleolus at the ankle",
    "Enters the tarsal tunnel beneath the flexor retinaculum",
    "Divides into medial and lateral plantar nerves",
    "Supplies sensation to the sole of the foot and toe flexor muscles"
  ],

  tarsalTunnelAnatomy: [
    "Bounded by medial malleolus and calcaneus (bony floor)",
    "Covered by flexor retinaculum (laciniate ligament)",
    "Contains posterior tibial nerve, artery, and veins",
    "Also houses tendons of tibialis posterior, flexor digitorum longus, and flexor hallucis longus",
    "Limited space makes nerve vulnerable to compression",
    "Any swelling or space-occupying lesion can cause symptoms"
  ],

  compressionSites: [
    "Proximal edge of flexor retinaculum",
    "Within the tarsal tunnel itself",
    "At the division into plantar nerves",
    "Around fascial bands or accessory muscles",
    "Near bony prominences or spurs",
    "Areas of scar tissue or adhesions"
  ],

  anatomyPoints: [
    {
      id: 'medial-malleolus',
      title: 'Medial Malleolus',
      description: 'Bony prominence on the inner ankle that forms part of the tarsal tunnel',
      clinicalSignificance: 'Key landmark for identifying the tarsal tunnel location during examination and procedures',
      location: { x: 25, y: 40 }
    },
    {
      id: 'flexor-retinaculum',
      title: 'Flexor Retinaculum',
      description: 'Fibrous band that forms the roof of the tarsal tunnel',
      clinicalSignificance: 'Primary structure that compresses the posterior tibial nerve in tarsal tunnel syndrome',
      location: { x: 35, y: 50 }
    },
    {
      id: 'posterior-tibial-nerve',
      title: 'Posterior Tibial Nerve',
      description: 'Mixed motor and sensory nerve that passes through the tarsal tunnel',
      clinicalSignificance: 'The nerve that becomes compressed, causing the characteristic symptoms of burning and numbness',
      location: { x: 40, y: 55 }
    },
    {
      id: 'plantar-nerves',
      title: 'Medial & Lateral Plantar Nerves',
      description: 'Terminal branches of the posterior tibial nerve that supply the foot',
      clinicalSignificance: 'Compression affects these branches, causing symptoms in specific areas of the foot sole',
      location: { x: 50, y: 70 }
    },
    {
      id: 'calcaneus',
      title: 'Calcaneus (Heel Bone)',
      description: 'Large heel bone that forms the floor of the tarsal tunnel',
      clinicalSignificance: 'Bony spurs or prominences can contribute to nerve compression within the tunnel',
      location: { x: 60, y: 80 }
    }
  ]
};

const TarsalAnatomySection: React.FC<TarsalAnatomySectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Brain className="w-4 h-4 mr-2" />
            Nerve Anatomy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {anatomyData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {anatomyData.subtitle}
          </p>
        </div>

        {/* Anatomy Tabs */}
        <Tabs defaultValue="pathway" className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-3 h-14"
          )}>
            <TabsTrigger 
              value="pathway"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <Zap className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Nerve Pathway</span>
            </TabsTrigger>
            <TabsTrigger 
              value="tunnel"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <Target className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Tarsal Tunnel</span>
            </TabsTrigger>
            <TabsTrigger 
              value="compression"
              className={cn(
                "flex items-center gap-2 font-medium",
                deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
              )}
            >
              <AlertTriangle className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
              <span className={deviceInfo.isMobile ? "text-center" : ""}>Compression Sites</span>
            </TabsTrigger>
          </TabsList>

          {/* Nerve Pathway Tab */}
          <TabsContent value="pathway" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Zap className="w-5 h-5 text-primary" />
                  Posterior Tibial Nerve Pathway
                </CardTitle>
                <p className="text-enhanced-body">
                  The posterior tibial nerve follows a specific anatomical course from the leg to the foot, 
                  making it vulnerable to compression at the tarsal tunnel.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.nervePathway.map((step, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                        <span className="text-primary font-semibold text-sm">{index + 1}</span>
                      </div>
                      <p className="text-enhanced-body">{step}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tarsal Tunnel Tab */}
          <TabsContent value="tunnel" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <Target className="w-5 h-5 text-info" />
                  Tarsal Tunnel Anatomy
                </CardTitle>
                <p className="text-enhanced-body">
                  The tarsal tunnel is a confined space where the posterior tibial nerve is most vulnerable to compression.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.tarsalTunnelAnatomy.map((detail, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <Target className="w-5 h-5 text-info mt-0.5 flex-shrink-0" />
                      <p className="text-enhanced-body">{detail}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Compression Sites Tab */}
          <TabsContent value="compression" className="space-y-8">
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="text-enhanced-heading flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-foreground" />
                  Common Compression Sites
                </CardTitle>
                <p className="text-enhanced-body">
                  Understanding where compression typically occurs helps guide diagnosis and treatment approaches.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {anatomyData.compressionSites.map((site, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-foreground mt-0.5 flex-shrink-0" />
                      <p className="text-enhanced-body">{site}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Interactive Anatomy Diagram */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Eye className="w-5 h-5 text-primary" />
              Interactive Anatomy Visualization
            </CardTitle>
            <p className="text-enhanced-body">
              Click on the anatomical points below to learn about key structures involved in tarsal tunnel syndrome.
            </p>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-8",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"
            )}>
              {/* Anatomy Image with Interactive Points */}
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-**********-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                  alt="Posterior tibial nerve anatomy and tarsal tunnel"
                  className="w-full h-auto rounded-lg"
                  fallbackSrc="https://images.pexels.com/photos/7659564/pexels-photo-7659564.jpeg?auto=compress&cs=tinysrgb&w=600"
                />
                
                {/* Interactive Points */}
                {anatomyData.anatomyPoints.map((point) => (
                  <Button
                    key={point.id}
                    onClick={() => setSelectedPoint(selectedPoint === point.id ? null : point.id)}
                    className={cn(
                      "absolute w-4 h-4 rounded-full border-2 transition-all duration-200",
                      selectedPoint === point.id
                        ? "bg-primary border-primary scale-125 shadow-lg"
                        : "bg-background border-primary hover:scale-110 hover:bg-primary/10"
                    )}
                    style={{
                      left: `${point.location.x}%`,
                      top: `${point.location.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    aria-label={`View information about ${point.title}`}
                  />
                ))}
              </div>

              {/* Anatomy Point Details */}
              <div className="space-y-4">
                {selectedPoint ? (
                  (() => {
                    const point = anatomyData.anatomyPoints.find(p => p.id === selectedPoint);
                    return point ? (
                      <Card className="medical-card border-l-4 border-l-primary">
                        <CardHeader>
                          <CardTitle className="text-enhanced-subheading">{point.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Description:</h4>
                            <p className="text-enhanced-body text-sm">{point.description}</p>
                          </div>
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Clinical Significance:</h4>
                            <p className="text-enhanced-body text-sm">{point.clinicalSignificance}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()
                ) : (
                  <Card className="medical-card">
                    <CardContent className="text-center py-8">
                      <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-enhanced-body">
                        Click on the anatomical points in the image to learn about key structures involved in tarsal tunnel syndrome.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Clinical Correlation */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Shield className="w-5 h-5 text-success" />
              Clinical-Anatomical Correlation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4">
                  <Footprints className="w-8 h-8 text-primary mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Symptom Distribution</h4>
                <p className="text-enhanced-body text-sm">
                  Nerve anatomy explains why symptoms occur in specific areas of the foot sole
                </p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Activity className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Compression Mechanism</h4>
                <p className="text-enhanced-body text-sm">
                  Understanding anatomy helps identify why certain positions worsen symptoms
                </p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Heart className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Treatment Rationale</h4>
                <p className="text-enhanced-body text-sm">
                  Anatomical knowledge guides effective treatment strategies and surgical approaches
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TarsalAnatomySection;
