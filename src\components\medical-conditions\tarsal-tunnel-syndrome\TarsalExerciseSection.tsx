import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Award, CheckCircle, Clock, Du<PERSON>bell, Heart, Play, RotateCcw, Shield, Target, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  duration: string;
  frequency: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  benefits: string[];
  precautions: string[];
  videoUrl?: string;
}

interface ExerciseCategory {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string; size?: number | string; }>;
  description: string;
  exercises: Exercise[];
}

interface TarsalExerciseSectionProps {
  className?: string;
}

const exerciseCategories: ExerciseCategory[] = [
  {
    id: 'nerve-gliding',
    title: 'Nerve Gliding Exercises',
    icon: Zap,
    description: 'Gentle movements to improve nerve mobility and reduce adhesions',
    exercises: [
      {
        id: 'tibial-nerve-glides',
        name: 'Posterior Tibial Nerve Glides',
        description: 'Gentle nerve mobilization to improve nerve sliding and reduce tension',
        instructions: [
          'Sit comfortably with your leg extended straight',
          'Keep your knee straight and foot relaxed',
          'Slowly pull your toes and foot toward your shin (dorsiflexion)',
          'Hold for 5 seconds, then point your foot down (plantarflexion)',
          'Add gentle ankle circles in both directions',
          'Perform slowly and smoothly without forcing movement'
        ],
        duration: '5-10 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Improves nerve mobility and sliding',
          'Reduces nerve adhesions and scar tissue',
          'Decreases nerve tension and irritation',
          'Promotes healing and blood flow to nerve'
        ],
        precautions: [
          'Stop if symptoms worsen during exercise',
          'Avoid aggressive or forceful movements',
          'Start gently and progress gradually'
        ]
      },
      {
        id: 'calf-nerve-stretch',
        name: 'Calf and Nerve Stretch',
        description: 'Combined calf stretching with nerve mobilization',
        instructions: [
          'Stand facing a wall with hands against the wall',
          'Step the affected foot back about 3 feet',
          'Keep the back heel flat on the ground',
          'Lean forward into the wall to stretch the calf',
          'Add ankle dorsiflexion to increase nerve stretch',
          'Hold for 30 seconds, repeat 3 times'
        ],
        duration: '3-5 minutes',
        frequency: '3-4 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Stretches calf muscles and nerve pathway',
          'Reduces tension on posterior tibial nerve',
          'Improves ankle flexibility',
          'Decreases muscle tightness'
        ],
        precautions: [
          'Don\'t bounce or force the stretch',
          'Stop if numbness or tingling increases',
          'Maintain steady breathing during stretch'
        ]
      }
    ]
  },
  {
    id: 'strengthening',
    title: 'Strengthening Exercises',
    icon: Dumbbell,
    description: 'Targeted exercises to strengthen foot and ankle muscles',
    exercises: [
      {
        id: 'intrinsic-foot-strengthening',
        name: 'Intrinsic Foot Muscle Strengthening',
        description: 'Strengthen the small muscles within the foot',
        instructions: [
          'Sit with feet flat on the floor',
          'Try to pick up marbles or small objects with your toes',
          'Perform toe curls by gripping a towel with your toes',
          'Spread your toes apart as wide as possible',
          'Try to lift each toe individually',
          'Progress to using resistance bands around toes'
        ],
        duration: '10-15 minutes',
        frequency: 'Daily',
        difficulty: 'Intermediate',
        benefits: [
          'Strengthens intrinsic foot muscles',
          'Improves foot stability and arch support',
          'Enhances proprioception and balance',
          'Reduces stress on posterior tibial nerve'
        ],
        precautions: [
          'Start with simple exercises before progressing',
          'Don\'t overwork muscles initially',
          'Stop if cramping occurs'
        ]
      },
      {
        id: 'arch-support-exercises',
        name: 'Arch Support Strengthening',
        description: 'Exercises to strengthen muscles supporting the foot arch',
        instructions: [
          'Stand with feet hip-width apart',
          'Try to lift your arch while keeping toes and heel down',
          'Hold the arch lift for 5 seconds',
          'Relax and repeat 10 times',
          'Progress to single-foot arch lifts',
          'Add calf raises while maintaining arch position'
        ],
        duration: '5-10 minutes',
        frequency: '2 times daily',
        difficulty: 'Intermediate',
        benefits: [
          'Strengthens posterior tibialis muscle',
          'Improves arch support and foot mechanics',
          'Reduces overpronation and nerve stress',
          'Enhances overall foot stability'
        ],
        precautions: [
          'Focus on quality over quantity',
          'Don\'t force the arch too high',
          'Progress gradually to avoid fatigue'
        ]
      }
    ]
  },
  {
    id: 'stretching',
    title: 'Flexibility & Stretching',
    icon: Activity,
    description: 'Stretches to improve flexibility and reduce muscle tension',
    exercises: [
      {
        id: 'plantar-fascia-stretch',
        name: 'Plantar Fascia and Foot Stretches',
        description: 'Comprehensive stretching for foot and ankle flexibility',
        instructions: [
          'Sit and cross the affected foot over the opposite knee',
          'Grasp your toes and gently pull them toward your shin',
          'Hold for 30 seconds, feeling stretch in arch',
          'Perform calf stretches against a wall',
          'Use a tennis ball to massage the foot sole',
          'Stretch the Achilles tendon with towel pulls'
        ],
        duration: '10-15 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Improves foot and ankle flexibility',
          'Reduces plantar fascia tension',
          'Decreases muscle tightness',
          'Promotes better foot mechanics'
        ],
        precautions: [
          'Stretch gently without forcing',
          'Hold stretches steadily, don\'t bounce',
          'Stop if pain increases'
        ]
      }
    ]
  },
  {
    id: 'functional',
    title: 'Functional Training',
    icon: Target,
    description: 'Real-world movements to improve daily function',
    exercises: [
      {
        id: 'balance-proprioception',
        name: 'Balance and Proprioception Training',
        description: 'Exercises to improve balance and foot awareness',
        instructions: [
          'Stand on one foot for 30 seconds',
          'Progress to standing on uneven surfaces',
          'Try single-leg stands with eyes closed',
          'Walk heel-to-toe in a straight line',
          'Practice standing on a balance pad or pillow',
          'Add arm movements while balancing'
        ],
        duration: '10-15 minutes',
        frequency: 'Daily',
        difficulty: 'Advanced',
        benefits: [
          'Improves balance and stability',
          'Enhances proprioception and foot awareness',
          'Strengthens stabilizing muscles',
          'Reduces fall risk and injury'
        ],
        precautions: [
          'Have support nearby when starting',
          'Progress difficulty gradually',
          'Stop if dizziness occurs'
        ]
      }
    ]
  }
];

const TarsalExerciseSection: React.FC<TarsalExerciseSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('nerve-gliding');
  const [_selectedExercise, _setSelectedExercise] = useState<string | null>(null);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-success-light text-foreground border border-success/30';
      case 'Intermediate': return 'bg-info-light text-foreground border border-info/30';
      case 'Advanced': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Dumbbell className="w-4 h-4 mr-2" />
            Exercise & Therapy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Exercise and Physical Therapy Guide
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive exercise program designed to improve nerve mobility, strengthen supporting muscles, 
            and enhance overall foot function for tarsal tunnel syndrome recovery
          </p>
        </div>

        {/* Exercise Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {exerciseCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {exerciseCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-center gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Exercises */}
              <div className="space-y-8">
                {category.exercises.map((exercise) => (
                  <Card key={exercise.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{exercise.name}</CardTitle>
                        <Badge className={getDifficultyColor(exercise.difficulty)}>
                          {exercise.difficulty}
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{exercise.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Exercise Details */}
                      <div className={cn(
                        "grid gap-4",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                          <Clock className="w-5 h-5 text-info mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Duration</h5>
                          <p className="text-enhanced-body text-sm">{exercise.duration}</p>
                        </div>
                        <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                          <RotateCcw className="w-5 h-5 text-success mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Frequency</h5>
                          <p className="text-enhanced-body text-sm">{exercise.frequency}</p>
                        </div>
                        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                          <Target className="w-5 h-5 text-primary mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Level</h5>
                          <p className="text-enhanced-body text-sm">{exercise.difficulty}</p>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                          <Play className="w-4 h-4 text-primary" />
                          Step-by-Step Instructions
                        </h4>
                        <ol className="space-y-2">
                          {exercise.instructions.map((instruction, index) => (
                            <li key={index} className="flex items-start gap-3">
                              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
                                <span className="text-primary font-semibold text-xs">{index + 1}</span>
                              </div>
                              <span className="text-enhanced-body text-sm">{instruction}</span>
                            </li>
                          ))}
                        </ol>
                      </div>

                      {/* Benefits and Precautions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Benefits
                          </h4>
                          <ul className="space-y-2">
                            {exercise.benefits.map((benefit, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Precautions
                          </h4>
                          <ul className="space-y-2">
                            {exercise.precautions.map((precaution, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{precaution}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Exercise Program Guidelines */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Award className="w-5 h-5 text-primary" />
              Exercise Program Guidelines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <TrendingUp className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Progressive Approach</h4>
                <p className="text-enhanced-body text-sm">Start with beginner exercises and gradually progress to more advanced movements</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Heart className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Listen to Your Body</h4>
                <p className="text-enhanced-body text-sm">Stop if symptoms worsen and adjust intensity based on your comfort level</p>
              </div>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Shield className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Consistency is Key</h4>
                <p className="text-enhanced-body text-sm">Regular daily exercise is more beneficial than occasional intense sessions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TarsalExerciseSection;
