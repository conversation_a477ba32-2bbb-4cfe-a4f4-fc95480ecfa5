import { Alert<PERSON><PERSON>gle, <PERSON><PERSON><PERSON>cle, Clock, Phone, Shield, Target, TrendingDown } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface _WarningSign {
  symptom: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'concerning';
  timeframe: string;
  action: string;
}

interface _ProgressionStage {
  stage: string;
  description: string;
  symptoms: string[];
  timeframe: string;
  reversibility: 'reversible' | 'partially-reversible' | 'permanent';
}

interface UlnarWarningSignsSectionProps {
  warningSignsData: {
    emergencySignsTitle: string;
    emergencySignsDescription: string;
    emergencySigns: WarningSign[];
    progressionStages: ProgressionStage[];
    whenToSeekHelp: {
      immediate: string[];
      urgent: string[];
      routine: string[];
    };
  };
  className?: string;
}

const UlnarWarningSignsSection: React.FC<UlnarWarningSignsSectionProps> = ({
  warningSignsData,
  className
}) => {
  const _deviceInfo = useDeviceDetection();

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'bg-muted-light text-foreground border border-border/70';
      case 'urgent': return 'bg-info-light text-foreground border border-info/30';
      case 'concerning': return 'bg-medical-blue-light text-medical-blue border border-medical-blue/30';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const _getReversibilityColor = (reversibility: string) => {
    switch (reversibility) {
      case 'reversible': return 'bg-success-light text-foreground border border-success/30';
      case 'partially-reversible': return 'bg-info-light text-foreground border border-info/30';
      case 'permanent': return 'bg-muted-light text-foreground border border-border/70';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return Phone;
      case 'urgent': return Clock;
      case 'concerning': return Eye;
      default: return AlertTriangle;
    }
  };

  return (
    <section className={cn("section-background py-16", className)}>
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading mb-4">
            {warningSignsData.emergencySignsTitle}
          </h2>
          <p className="text-enhanced-body max-w-3xl mx-auto leading-relaxed">
            {warningSignsData.emergencySignsDescription}
          </p>
        </div>

        {/* Emergency Contact Card */}
        <Card className="medical-card border-l-4 border-l-primary mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Phone className="w-5 h-5 text-primary" />
              Emergency Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <Phone className="w-8 h-8 text-foreground mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Emergency</h4>
                <p className="text-enhanced-body font-bold">000</p>
                <p className="text-enhanced-caption">Severe symptoms</p>
              </div>
              <div className="text-center">
                <Clock className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Urgent Care</h4>
                <p className="text-enhanced-body font-bold">(02) 9999 0000</p>
                <p className="text-enhanced-caption">Same-day appointment</p>
              </div>
              <div className="text-center">
                <Shield className="w-8 h-8 text-info mx-auto mb-2" />
                <h4 className="text-enhanced-subheading mb-1">Clinic</h4>
                <p className="text-enhanced-body font-bold">(02) 8888 0000</p>
                <p className="text-enhanced-caption">Regular consultation</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Warning Signs Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {warningSignsData.emergencySigns.map((sign, index) => {
            const _UrgencyIcon = getUrgencyIcon(sign.urgency);
            return (
              <Card key={index} className={cn("medical-card border-l-4", getUrgencyColor(sign.urgency))}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-enhanced-heading flex items-center gap-3 flex-1">
                      <UrgencyIcon className="w-5 h-5" />
                      {sign.symptom}
                    </CardTitle>
                    <Badge variant={sign.urgency === 'immediate' ? 'emergency' : sign.urgency === 'urgent' ? 'urgent' : 'routine'}>
                      {sign.urgency}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-enhanced-body text-sm mb-4">
                    {sign.description}
                  </p>
                  
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-enhanced-caption">Timeframe</span>
                      </div>
                      <p className="text-enhanced-body text-sm font-medium">{sign.timeframe}</p>
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <Target className="w-4 h-4 text-muted-foreground" />
                        <span className="text-enhanced-caption">Action Required</span>
                      </div>
                      <p className="text-enhanced-body text-sm font-medium">{sign.action}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Progression Stages */}
        <Card className="medical-card mb-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <TrendingDown className="w-5 h-5 text-info" />
              Disease Progression Stages
            </CardTitle>
            <p className="text-enhanced-body">
              Understanding the progression of ulnar neuropathy helps in early intervention and treatment planning.
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {warningSignsData.progressionStages.map((stage, index) => (
                <div key={index} className="relative">
                  {index < warningSignsData.progressionStages.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border" />
                  )}
                  
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center font-bold flex-shrink-0">
                      {index + 1}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-enhanced-subheading">{stage.stage}</h4>
                        <Badge variant={stage.reversibility === 'reversible' ? 'routine' : stage.reversibility === 'partially-reversible' ? 'urgent' : 'emergency'}>
                          {stage.reversibility.replace('-', ' ')}
                        </Badge>
                      </div>
                      
                      <p className="text-enhanced-body text-sm mb-3">{stage.description}</p>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="text-enhanced-caption mb-2">Symptoms</h5>
                          <ul className="space-y-1">
                            {stage.symptoms.map((symptom, symptomIndex) => (
                              <li key={symptomIndex} className="flex items-start gap-2">
                                <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{symptom}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h5 className="text-enhanced-caption mb-2">Typical Timeframe</h5>
                          <p className="text-enhanced-body text-sm font-medium">{stage.timeframe}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* When to Seek Help */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card className="medical-card border-l-4 border-l-muted">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-3">
                <Phone className="w-5 h-5 text-foreground" />
                Immediate Care
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {warningSignsData.whenToSeekHelp.immediate.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="medical-card border-l-4 border-l-info">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-3">
                <Clock className="w-5 h-5 text-info" />
                Urgent Care
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {warningSignsData.whenToSeekHelp.urgent.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Clock className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="medical-card border-l-4 border-l-info">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-info" />
                Routine Care
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {warningSignsData.whenToSeekHelp.routine.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default UlnarWarningSignsSection;
