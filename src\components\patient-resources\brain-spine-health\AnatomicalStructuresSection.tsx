import React from 'react';

import SafeImage from '@/components/SafeImage';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';

interface AnatomicalStructure {
  id: string;
  name: string;
  description: string;
  function: string;
  pathophysiology: string;
  image: string;
  category: 'brain' | 'spine' | 'nerves';
}

interface AnatomicalStructuresSectionProps {
  structures: AnatomicalStructure[];
}

const AnatomicalStructuresSection: React.FC<AnatomicalStructuresSectionProps> = ({ structures }) => {
  const getIconForCategory = (category: string) => {
    switch (category) {
      case 'brain': return Brain;
      case 'spine': return Bone;
      case 'nerves': return Network;
      default: return Zap;
    }
  };

  const categorizedStructures = {
    brain: structures.filter(s => s.category === 'brain'),
    spine: structures.filter(s => s.category === 'spine'),
    nerves: structures.filter(s => s.category === 'nerves')
  };

  return (
    <TabsContent value="anatomy" className="mt-8">
      <div className="grid gap-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-enhanced-heading mb-4">
            Neuroanatomical Structures
          </h3>
          <p className="text-muted-foreground max-w-3xl mx-auto">
            Understanding the anatomy of the nervous system is fundamental to comprehending
            neurological conditions and their treatments.
          </p>
        </div>

        {Object.entries(categorizedStructures).map(([category, categoryStructures]) => (
          <div key={category} className="space-y-6">
            <h4 className="text-xl font-semibold text-enhanced-heading capitalize">
              {category} Structures
            </h4>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {categoryStructures.map((structure) => {
                const Icon = getIconForCategory(structure.category);
                return (
                  <Card key={structure.id} className="medical-card h-full">
                    <CardHeader className="text-center">
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <Icon className="h-6 w-6 text-primary" />
                        <CardTitle className="text-lg">{structure.name}</CardTitle>
                      </div>
                      <SafeImage
                        src={structure.image}
                        alt={`${structure.name} anatomy`}
                        className="w-full h-48 object-cover rounded-lg"
                        fallbackSrc="/images/anatomy/spine-default.jpg"
                      />
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-primary mb-2">Description</h4>
                        <p className="text-sm text-muted-foreground">{structure.description}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-primary mb-2">Function</h4>
                        <p className="text-sm text-muted-foreground">{structure.function}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm text-primary mb-2">Pathophysiology</h4>
                        <p className="text-sm text-muted-foreground">{structure.pathophysiology}</p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </TabsContent>
  );
};

export default AnatomicalStructuresSection;
