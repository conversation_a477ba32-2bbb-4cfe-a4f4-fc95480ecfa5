import { Activity, ChevronDown, ChevronUp, Info } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface AnatomyComponent {
  component: string;
  description: string;
  normalFunction: string[];
  pathologyEffects: string[];
  type?: 'brain' | 'spine' | 'nerve' | 'vascular';
}

interface AnatomySectionBaseProps {
  title: string;
  description: string;
  components: AnatomyComponent[];
  className?: string;
  pathologyName?: string; // e.g., "malformation", "compression", "tumor"
}

/**
 * Base Anatomy Section Component
 * Reusable component for displaying anatomical information across different medical conditions
 */
export const AnatomySectionBase: React.FC<AnatomySectionBaseProps> = ({
  title,
  description,
  components,
  className,
  pathologyName = "condition"
}) => {
  const deviceInfo = useDeviceDetection();
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleExpanded = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getComponentIcon = (component: AnatomyComponent) => {
    const name = component.component.toLowerCase();
    const type = component.type;
    
    // Type-based icons
    if (type === 'brain') return Brain;
    if (type === 'spine') return Layers;
    if (type === 'nerve') return Zap;
    if (type === 'vascular') return Droplets;
    
    // Name-based fallbacks
    if (name.includes('brain') || name.includes('cerebral')) return Brain;
    if (name.includes('spine') || name.includes('vertebra')) return Layers;
    if (name.includes('nerve') || name.includes('neural')) return Zap;
    if (name.includes('vessel') || name.includes('artery') || name.includes('vein')) return Droplets;
    if (name.includes('nidus') || name.includes('core')) return Hexagon;
    if (name.includes('network') || name.includes('connection')) return Network;
    
    return Activity;
  };

  const getComponentBadgeColor = (component: AnatomyComponent) => {
    const type = component.type;
    switch (type) {
      case 'brain': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'spine': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'nerve': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'vascular': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (!components || components.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No anatomical information available.</p>
      </div>
    );
  }

  return (
    <section className={cn("py-16 bg-gradient-to-br from-muted/10 to-background", className)}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-enhanced-body max-w-3xl mx-auto">
            {description}
          </p>
        </div>

        <div className={cn(
          "grid gap-6",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
        )}>
          {components.map((component, _index) => {
            const Icon = getComponentIcon(component);
            const isExpanded = expandedComponent === component.component;
            
            return (
              <Card key={component.component} className="medical-card">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 rounded-full bg-primary/10">
                        <Icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        {component.type && (
                          <Badge 
                            variant="secondary" 
                            className={cn("text-xs mt-1", getComponentBadgeColor(component))}
                          >
                            {component.type}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(component.component)}
                      className="ml-2"
                    >
                      {isExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <CardDescription>{component.description}</CardDescription>
                </CardHeader>

                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className="space-y-6">
                        {/* Normal Function */}
                        <div>
                          <h4 className="font-semibold text-green-700 dark:text-green-400 mb-3 flex items-center gap-2">
                            <Activity className="h-4 w-4" />
                            Normal Function
                          </h4>
                          <ul className="space-y-2">
                            {component.normalFunction.map((func, funcIndex) => (
                              <li key={funcIndex} className="text-sm text-muted-foreground flex items-start gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-green-500 mt-2 flex-shrink-0" />
                                {func}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Pathology Effects */}
                        <div>
                          <h4 className="font-semibold text-red-700 dark:text-red-400 mb-3 flex items-center gap-2">
                            <Info className="h-4 w-4" />
                            Effects of {pathologyName}
                          </h4>
                          <ul className="space-y-2">
                            {component.pathologyEffects.map((effect, effectIndex) => (
                              <li key={effectIndex} className="text-sm text-muted-foreground flex items-start gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-red-500 mt-2 flex-shrink-0" />
                                {effect}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Educational Note */}
        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto bg-muted/30 border-primary/20">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-2 mb-3">
                <Info className="h-5 w-5 text-primary" />
                <h4 className="font-semibold text-primary">Educational Information</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                This anatomical overview is provided for educational purposes. 
                Individual anatomy may vary, and specific medical advice should always 
                be sought from qualified healthcare professionals.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default AnatomySectionBase;
