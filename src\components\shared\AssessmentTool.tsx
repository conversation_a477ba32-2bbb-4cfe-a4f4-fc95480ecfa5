import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, <PERSON><PERSON>eft, Activity } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';

export interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

export interface AssessmentResult {
  totalScore: number;
  level: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'immediate';
  nextSteps: string[];
  factors: string[];
}

interface AssessmentToolProps {
  title: string;
  description: string;
  icon?: React.ComponentType<{ className?: string }>;
  questions: AssessmentQuestion[];
  calculateResult: (answers: Record<string, string>) => AssessmentResult;
  className?: string;
}

export function AssessmentTool({
  title,
  description,
  icon: Icon = Activity,
  questions,
  calculateResult,
  className
}: AssessmentToolProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [result, setResult] = useState<AssessmentResult | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);

  const formId = useId();

  const progress = ((currentStep + 1) / questions.length) * 100;
  const currentQuestion = questions[currentStep];

  const handleAnswer = (value: string) => {
    const newAnswers = { ...answers, [currentQuestion.id]: value };
    setAnswers(newAnswers);
  };

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Calculate final result
      const finalResult = calculateResult(answers);
      setResult(finalResult);
      setIsCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleRestart = () => {
    setCurrentStep(0);
    setAnswers({});
    setResult(null);
    setIsCompleted(false);
  };

  const canProceed = answers[currentQuestion?.id];

  if (isCompleted && result) {
    return (
      <Card className={cn("w-full max-w-4xl mx-auto", className)}>
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">Assessment Complete</CardTitle>
          <CardDescription>
            Based on your responses, here are your results
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Risk Level Badge */}
          <div className="text-center">
            <Badge 
              variant={result.level === 'high' ? 'destructive' : result.level === 'moderate' ? 'default' : 'secondary'}
              className="text-lg px-4 py-2"
            >
              {result.level.charAt(0).toUpperCase() + result.level.slice(1)} Level
            </Badge>
          </div>

          {/* Recommendation */}
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Recommendation</h3>
            <p className="text-blue-800">{result.recommendation}</p>
          </div>

          {/* Urgency */}
          <div className={cn(
            "p-6 rounded-lg",
            result.urgency === 'immediate' ? 'bg-red-50' : 
            result.urgency === 'urgent' ? 'bg-orange-50' : 'bg-green-50'
          )}>
            <h3 className={cn(
              "font-semibold mb-2",
              result.urgency === 'immediate' ? 'text-red-900' : 
              result.urgency === 'urgent' ? 'text-orange-900' : 'text-green-900'
            )}>
              Urgency: {result.urgency.charAt(0).toUpperCase() + result.urgency.slice(1)}
            </h3>
          </div>

          {/* Next Steps */}
          {result.nextSteps.length > 0 && (
            <div>
              <h3 className="font-semibold mb-3">Recommended Next Steps</h3>
              <ul className="space-y-2">
                {result.nextSteps.map((step, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>{step}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Factors */}
          {result.factors.length > 0 && (
            <div>
              <h3 className="font-semibold mb-3">Key Factors Identified</h3>
              <div className="flex flex-wrap gap-2">
                {result.factors.map((factor, index) => (
                  <Badge key={index} variant="outline">
                    {factor}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-4 pt-6">
            <Button onClick={handleRestart} variant="outline" className="flex-1">
              Take Assessment Again
            </Button>
            <Button className="flex-1">
              Book Consultation
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full max-w-4xl mx-auto", className)}>
      <CardHeader>
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Icon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
        
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Question {currentStep + 1} of {questions.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Question */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">
              {currentQuestion.question}
            </h3>
            {currentQuestion.description && (
              <p className="text-gray-600 text-sm">
                {currentQuestion.description}
              </p>
            )}
          </div>

          {/* Answer Options */}
          <RadioGroup
            value={answers[currentQuestion.id] || ''}
            onValueChange={handleAnswer}
            className="space-y-3"
          >
            {currentQuestion.options.map((option) => (
              <div key={option.value} className="flex items-start space-x-3">
                <RadioGroupItem 
                  value={option.value} 
                  id={`${formId}-${option.value}`}
                  className="mt-1"
                />
                <Label 
                  htmlFor={`${formId}-${option.value}`}
                  className="flex-1 cursor-pointer text-sm leading-relaxed"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className="flex items-center gap-2"
          >
            {currentStep === questions.length - 1 ? 'Complete Assessment' : 'Next'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
