import React from 'react';

import { useIsMobile, useHasHover } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

/**
 * Common Layout Patterns
 * Extracts layout patterns found repeatedly throughout the codebase
 * Provides consistent spacing, responsive behaviour, and device-aware styling
 */

export interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

/**
 * Optimized responsive container with CSS-first approach
 * Uses selective device detection only when necessary
 */
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = React.memo(({
  children,
  className,
  size = 'lg'
}) => {
  // Use selective hook instead of full device context
  const isMobile = useIsMobile();

  const sizeClasses = {
    sm: 'container max-w-2xl',
    md: 'container max-w-4xl',
    lg: 'container max-w-6xl',
    xl: 'container max-w-7xl',
    full: 'container',
  };

  // Apply mobile-specific class only when needed
  const containerClass = cn(
    sizeClasses[size],
    isMobile && 'mobile-container',
    className
  );

  return (
    <div className={containerClass}>
      {children}
    </div>
  );
});

export interface ResponsiveSectionProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  background?: 'default' | 'muted' | 'card';
  id?: string;
}

/**
 * Optimized responsive section with CSS-first approach
 * Uses selective device detection only when necessary
 */
export const ResponsiveSection: React.FC<ResponsiveSectionProps> = React.memo(({
  children,
  className,
  spacing = 'md',
  background = 'default',
  id
}) => {
  // Use selective hook instead of full device context
  const isMobile = useIsMobile();

  const spacingClasses = {
    none: '',
    sm: 'py-8',
    md: 'py-16',
    lg: 'py-24',
    xl: 'py-32',
  };

  const mobileSpacingClasses = {
    none: '',
    sm: 'mobile-section-sm',
    md: 'mobile-section',
    lg: 'mobile-section-lg',
    xl: 'mobile-section-xl',
  };

  const backgroundClasses = {
    default: '',
    muted: 'bg-muted/30',
    card: 'bg-card',
  };

  return (
    <section
      id={id}
      className={cn(
        isMobile ? mobileSpacingClasses[spacing] : spacingClasses[spacing],
        backgroundClasses[background],
        className
      )}
    >
      {children}
    </section>
  );
});

export interface FlexLayoutProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  wrap?: boolean;
  className?: string;
}

/**
 * Optimized flexible layout component with CSS-first responsive behaviour
 */
export const FlexLayout: React.FC<FlexLayoutProps> = React.memo(({
  children,
  direction = 'row',
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  className
}) => {
  // Use selective hook only for direction changes
  const isMobile = useIsMobile();

  const directionClasses = {
    row: isMobile ? 'flex-col' : 'flex-row',
    col: 'flex-col',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  const mobileGapClasses = {
    none: 'gap-0',
    sm: 'gap-mobile-sm',
    md: 'gap-mobile-md',
    lg: 'gap-mobile-lg',
    xl: 'gap-mobile-xl',
  };

  return (
    <div className={cn(
      'flex',
      directionClasses[direction],
      alignClasses[align],
      justifyClasses[justify],
      isMobile ? mobileGapClasses[gap] : gapClasses[gap],
      wrap && 'flex-wrap',
      className
    )}>
      {children}
    </div>
  );
});

export interface CardLayoutProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  interactive?: boolean;
}

/**
 * Optimized card layout with CSS-first responsive behaviour
 */
export const CardLayout: React.FC<CardLayoutProps> = React.memo(({
  children,
  variant = 'default',
  padding = 'md',
  className,
  interactive = false
}) => {
  // Use selective hooks for specific needs
  const isMobile = useIsMobile();
  const hasHover = useHasHover();

  const variantClasses = {
    default: 'bg-card border border-border',
    elevated: 'bg-card shadow-lg border border-border',
    outlined: 'bg-transparent border-2 border-border',
    ghost: 'bg-transparent',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
  };

  const mobilePaddingClasses = {
    none: '',
    sm: 'p-mobile-sm',
    md: 'p-mobile-md',
    lg: 'p-mobile-lg',
  };

  const interactiveClasses = interactive
    ? isMobile
      ? 'touch-feedback'
      : hasHover
      ? 'hover:shadow-xl transition-all duration-300 hover:scale-105'
      : 'focus:shadow-xl transition-all duration-300 focus:scale-105'
    : '';

  return (
    <div className={cn(
      'rounded-lg',
      variantClasses[variant],
      isMobile ? mobilePaddingClasses[padding] : paddingClasses[padding],
      interactiveClasses,
      className
    )}>
      {children}
    </div>
  );
});

export interface AnimatedContentProps {
  children: React.ReactNode;
  animation?: 'fade' | 'slide' | 'scale' | 'none';
  delay?: number;
  className?: string;
}

/**
 * Optimized animated content wrapper with reduced motion support
 */
export const AnimatedContent: React.FC<AnimatedContentProps> = React.memo(({
  children,
  animation = 'fade',
  delay = 0,
  className
}) => {
  const animationClasses = {
    fade: 'animate-fade-in',
    slide: 'animate-slide-in',
    scale: 'animate-scale-in',
    none: '',
  };

  return (
    <div
      className={cn(
        animationClasses[animation],
        'motion-reduce:animate-none', // Respect reduced motion preference
        className
      )}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
});

export interface CenteredContentProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'none';
  className?: string;
}

/**
 * Optimized centered content with responsive max-width (CSS-only)
 */
export const CenteredContent: React.FC<CenteredContentProps> = React.memo(({
  children,
  maxWidth = 'lg',
  className
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    none: '',
  };

  return (
    <div className={cn(
      'mx-auto text-center',
      maxWidthClasses[maxWidth],
      className
    )}>
      {children}
    </div>
  );
});

export interface StackLayoutProps {
  children: React.ReactNode;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

/**
 * Optimized vertical stack layout with selective device detection
 */
export const StackLayout: React.FC<StackLayoutProps> = React.memo(({
  children,
  spacing = 'md',
  className
}) => {
  // Use selective hook instead of full device context
  const isMobile = useIsMobile();

  const spacingClasses = {
    none: 'space-y-0',
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8',
  };

  const mobileSpacingClasses = {
    none: 'space-y-0',
    sm: 'space-y-mobile-sm',
    md: 'space-y-mobile-md',
    lg: 'space-y-mobile-lg',
    xl: 'space-y-mobile-xl',
  };

  return (
    <div className={cn(
      isMobile ? mobileSpacingClasses[spacing] : spacingClasses[spacing],
      className
    )}>
      {children}
    </div>
  );
});

export default {
  ResponsiveContainer,
  ResponsiveSection,
  FlexLayout,
  CardLayout,
  AnimatedContent,
  CenteredContent,
  StackLayout,
};
