import { Check, X, Info, Star } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface TreatmentOption {
  id: string;
  name: string;
  description: string;
  type: 'surgical' | 'non-surgical' | 'minimally-invasive';
  effectiveness: number; // 1-5 scale
  invasiveness: number; // 1-5 scale
  recoveryTime: string;
  suitability: string[];
  advantages: string[];
  disadvantages: string[];
  considerations: string[];
  isRecommended?: boolean;
}

interface TreatmentComparisonProps {
  title: string;
  description: string;
  treatments: TreatmentOption[];
  className?: string;
}

export function TreatmentComparison({
  title,
  description,
  treatments,
  className
}: TreatmentComparisonProps) {
  const getTypeColor = (type: TreatmentOption['type']) => {
    switch (type) {
      case 'surgical':
        return 'bg-red-100 text-red-800';
      case 'minimally-invasive':
        return 'bg-blue-100 text-blue-800';
      case 'non-surgical':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-4 w-4",
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">{title}</h2>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {treatments.map((treatment) => (
          <Card 
            key={treatment.id} 
            className={cn(
              "relative",
              treatment.isRecommended && "ring-2 ring-blue-500"
            )}
          >
            {treatment.isRecommended && (
              <div className="absolute -top-3 left-4">
                <Badge className="bg-blue-500 text-white">
                  Recommended
                </Badge>
              </div>
            )}
            
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{treatment.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {treatment.description}
                  </CardDescription>
                </div>
              </div>
              
              <div className="flex items-center gap-2 mt-3">
                <Badge className={getTypeColor(treatment.type)}>
                  {treatment.type.replace('-', ' ')}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Effectiveness Rating */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">Effectiveness</span>
                  <div className="flex items-center gap-1">
                    {renderStars(treatment.effectiveness)}
                  </div>
                </div>
              </div>

              {/* Invasiveness Rating */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">Invasiveness</span>
                  <div className="flex items-center gap-1">
                    {renderStars(treatment.invasiveness)}
                  </div>
                </div>
              </div>

              {/* Recovery Time */}
              <div>
                <span className="text-sm font-medium">Recovery Time: </span>
                <span className="text-sm text-gray-600">{treatment.recoveryTime}</span>
              </div>

              {/* Suitability */}
              {treatment.suitability.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Best For:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {treatment.suitability.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Info className="h-3 w-3 mt-1 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Advantages */}
              {treatment.advantages.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 text-green-700">Advantages:</h4>
                  <ul className="text-sm space-y-1">
                    {treatment.advantages.map((advantage, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Check className="h-3 w-3 mt-1 text-green-600 flex-shrink-0" />
                        <span className="text-green-700">{advantage}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Disadvantages */}
              {treatment.disadvantages.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2 text-red-700">Considerations:</h4>
                  <ul className="text-sm space-y-1">
                    {treatment.disadvantages.map((disadvantage, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <X className="h-3 w-3 mt-1 text-red-600 flex-shrink-0" />
                        <span className="text-red-700">{disadvantage}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Additional Considerations */}
              {treatment.considerations.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Important Notes:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {treatment.considerations.map((consideration, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Info className="h-3 w-3 mt-1 flex-shrink-0" />
                        <span>{consideration}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Disclaimer */}
      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start gap-2">
          <Info className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium mb-1">Important Disclaimer</p>
            <p>
              This comparison is for educational purposes only. Treatment selection depends on many individual factors 
              including your specific condition, medical history, and personal circumstances. Always consult with a 
              qualified neurosurgeon for personalized treatment recommendations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
