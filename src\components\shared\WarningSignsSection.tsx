import { Alert<PERSON>riangle, Phone, Clock, ArrowRight } from 'lucide-react';
import React from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface WarningSign {
  id: string;
  symptom: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'concerning';
  category?: string;
}

interface WarningSignsSectionProps {
  title: string;
  description: string;
  warningSigns: WarningSign[];
  emergencyNumber?: string;
  className?: string;
}

export function WarningSignsSection({
  title,
  description,
  warningSigns,
  emergencyNumber = "000",
  className
}: WarningSignsSectionProps) {
  const immediateWarnings = warningSigns.filter(sign => sign.urgency === 'immediate');
  const urgentWarnings = warningSigns.filter(sign => sign.urgency === 'urgent');
  const concerningWarnings = warningSigns.filter(sign => sign.urgency === 'concerning');

  const getUrgencyColor = (urgency: WarningSign['urgency']) => {
    switch (urgency) {
      case 'immediate':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'urgent':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'concerning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: WarningSign['urgency']) => {
    switch (urgency) {
      case 'immediate':
        return <Phone className="h-4 w-4" />;
      case 'urgent':
        return <Clock className="h-4 w-4" />;
      case 'concerning':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const renderWarningGroup = (warnings: WarningSign[], groupTitle: string, urgency: WarningSign['urgency']) => {
    if (warnings.length === 0) return null;

    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <div className={cn("p-1 rounded", getUrgencyColor(urgency))}>
            {getUrgencyIcon(urgency)}
          </div>
          <h3 className="font-semibold text-lg">{groupTitle}</h3>
        </div>
        
        <div className="grid gap-3">
          {warnings.map((warning) => (
            <Card key={warning.id} className={cn("border-l-4", getUrgencyColor(urgency))}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{warning.symptom}</h4>
                    <p className="text-sm text-gray-600">{warning.description}</p>
                    {warning.category && (
                      <Badge variant="outline" className="mt-2 text-xs">
                        {warning.category}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">{title}</h2>
        <p className="text-gray-600">{description}</p>
      </div>

      {/* Emergency Alert */}
      {immediateWarnings.length > 0 && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Emergency Warning:</strong> If you experience any of the symptoms below, 
            call {emergencyNumber} immediately or go to the nearest emergency department.
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-8">
        {/* Immediate/Emergency Signs */}
        {renderWarningGroup(
          immediateWarnings, 
          "🚨 Call Emergency Services Immediately", 
          'immediate'
        )}

        {/* Urgent Signs */}
        {renderWarningGroup(
          urgentWarnings, 
          "⚠️ Seek Medical Attention Within 24 Hours", 
          'urgent'
        )}

        {/* Concerning Signs */}
        {renderWarningGroup(
          concerningWarnings, 
          "💡 Schedule Medical Consultation", 
          'concerning'
        )}
      </div>

      {/* Action Buttons */}
      <div className="mt-8 flex flex-col sm:flex-row gap-4">
        {immediateWarnings.length > 0 && (
          <Button 
            variant="destructive" 
            className="flex items-center gap-2"
            onClick={() => window.open(`tel:${emergencyNumber}`)}
          >
            <Phone className="h-4 w-4" />
            Call {emergencyNumber}
          </Button>
        )}
        
        <Button variant="outline" className="flex items-center gap-2">
          Book Consultation
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* General Advice */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start gap-2">
          <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">When in Doubt, Seek Help</p>
            <p>
              These warning signs are guidelines only. Trust your instincts - if something feels wrong 
              or you're concerned about any symptoms, don't hesitate to seek medical attention. 
              Early intervention can make a significant difference in outcomes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
