/**
 * Shared Components Index
 * Centralised exports for all shared/common components and utilities
 * Updated during Foundation Phase refactoring
 */

// Existing shared components
export { default as SectionHeader } from './SectionHeader';
export { default as GlassCard } from './GlassCard';
export { default as IconContainer } from './IconContainer';
export { default as SectionContainer } from './SectionContainer';
export { AssessmentTool } from './AssessmentTool';
export { TreatmentComparison } from './TreatmentComparison';
export { WarningSignsSection } from './WarningSignsSection';
export type { AssessmentQuestion, AssessmentResult } from './AssessmentTool';
export type { TreatmentOption } from './TreatmentComparison';
export type { WarningSign } from './WarningSignsSection';
export { default as FeatureItem } from './FeatureItem';
export { default as ContactInfoItem } from './ContactInfoItem';

// New Foundation Phase Components

// Error Handling Components
export {
  StandardErrorBoundary,
  withErrorBoundary,
  useErrorHandler,
  SimpleErrorFallback
} from './StandardErrorBoundary';

// Common Section Patterns
export {
  SectionHeader as NewSectionHeader,
  ServiceCard,
  TwoColumnLayout,
  GridLayout,
  CallToAction
} from './CommonSectionPatterns';

// Layout Patterns
export {
  ResponsiveContainer,
  ResponsiveSection,
  FlexLayout,
  CardLayout,
  AnimatedContent,
  CenteredContent,
  StackLayout
} from './CommonLayoutPatterns';

// Medical Icons
export {
  MinimallyInvasiveIcon,
  ImagingNavigationIcon,
  SurgicalAdvantagesIcon,
  RoboticSurgeryIcon,
  BrainConditionsIcon,
  SpinalProblemsIcon,
  NerveProblemsIcon,
  MedicoLegalIcon
} from './MedicalIcons';

// Type exports
export type { IconProps } from './MedicalIcons';
export type {
  SectionHeaderProps,
  ServiceCardProps,
  TwoColumnLayoutProps,
  GridLayoutProps,
  CallToActionProps
} from './CommonSectionPatterns';
export type {
  ResponsiveContainerProps,
  ResponsiveSectionProps,
  FlexLayoutProps,
  CardLayoutProps,
  AnimatedContentProps,
  CenteredContentProps,
  StackLayoutProps
} from './CommonLayoutPatterns';
export type {
  ErrorBoundaryState,
  StandardErrorBoundaryProps
} from './StandardErrorBoundary';
