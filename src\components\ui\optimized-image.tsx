import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Optimized Image Component
 * Provides lazy loading, WebP support, and performance optimizations
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  width,
  height,
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  quality = 85,
  loading = 'lazy',
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observerRef.current?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [priority, isInView]);

  // Generate WebP source if supported
  const getWebPSrc = (originalSrc: string) => {
    if (originalSrc.includes('.webp')) return originalSrc;
    
    // Convert extension to WebP
    const webpSrc = originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
    return webpSrc;
  };

  // Generate responsive sizes
  const getResponsiveSrc = (originalSrc: string, size?: string) => {
    if (!size) return originalSrc;
    
    const ext = originalSrc.split('.').pop();
    const baseName = originalSrc.replace(`.${ext}`, '');
    return `${baseName}-${size}.${ext}`;
  };

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Placeholder component
  const Placeholder = () => (
    <div
      className={cn(
        'bg-muted animate-pulse flex items-center justify-center',
        className
      )}
      style={{ width, height }}
    >
      <svg
        className="w-8 h-8 text-muted-foreground"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );

  // Error component
  const ErrorFallback = () => (
    <div
      className={cn(
        'bg-muted border border-border rounded flex items-center justify-center text-muted-foreground',
        className
      )}
      style={{ width, height }}
    >
      <span className="text-sm">Failed to load image</span>
    </div>
  );

  // Don't render anything until in view (for lazy loading)
  if (!isInView && !priority) {
    return (
      <div
        ref={imgRef}
        className={cn('bg-muted', className)}
        style={{ width, height }}
      />
    );
  }

  // Show error state
  if (hasError) {
    return <ErrorFallback />;
  }

  return (
    <div className="relative">
      {/* Blur placeholder */}
      {placeholder === 'blur' && !isLoaded && (
        <div
          className={cn(
            'absolute inset-0 bg-muted animate-pulse',
            className
          )}
          style={{
            backgroundImage: blurDataURL ? `url(${blurDataURL})` : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: 'blur(10px)',
          }}
        />
      )}

      {/* Empty placeholder */}
      {placeholder === 'empty' && !isLoaded && <Placeholder />}

      {/* Main image with WebP support */}
      <picture>
        {/* WebP source for modern browsers */}
        <source
          srcSet={getWebPSrc(src)}
          type="image/webp"
          sizes={sizes}
        />
        
        {/* Fallback for browsers that don't support WebP */}
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          width={width}
          height={height}
          loading={loading}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          sizes={sizes}
        />
      </picture>
    </div>
  );
};

/**
 * Hook for preloading critical images
 */
export const useImagePreload = (src: string, priority = false) => {
  useEffect(() => {
    if (!priority) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, [src, priority]);
};

/**
 * Utility function to generate responsive image sizes
 */
export const generateResponsiveSizes = (
  baseSrc: string,
  breakpoints: { size: string; width: number }[]
) => {
  return breakpoints
    .map(({ size, width }) => `(max-width: ${width}px) ${size}`)
    .join(', ');
};

export default OptimizedImage;
