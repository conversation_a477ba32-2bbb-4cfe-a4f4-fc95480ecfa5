
export interface HealthResource {
  id: string;
  name: string;
  description: string;
  type: 'assessment' | 'education' | 'program' | 'anatomy' | 'recovery';
  requiresLogin: boolean;
  path: string;
  category: string;
  featured: boolean;
}

export interface AnatomicalStructure {
  id: string;
  name: string;
  description: string;
  function: string;
  pathophysiology: string;
  image: string;
  category: 'brain' | 'spine' | 'nerves';
}

export interface CommonCondition {
  id: string;
  name: string;
  description: string;
  symptoms: string[];
  causes: string[];
  treatments: string[];
  urgency: 'routine' | 'urgent' | 'emergency';
  path: string;
}

export interface LifestyleCategory {
  title: string;
  description: string;
  tips: string[];
  icon: React.ComponentType<{ className?: string }>;
}

// Comprehensive anatomical structures with pathophysiology
export const anatomicalStructures: AnatomicalStructure[] = [
  {
    id: 'cerebral-cortex',
    name: 'Ce<PERSON>bral Cortex',
    description: 'The outer layer of the brain responsible for higher cognitive functions, sensory processing, and motor control.',
    function: 'Controls conscious thought, language, memory, personality, and voluntary movements. Divided into four lobes: frontal, parietal, temporal, and occipital.',
    pathophysiology: 'Damage to the cortex can result from stroke, trauma, tumours, or degenerative diseases. Different regions control specific functions, so localised damage produces predictable deficits.',
    clinicalSignificance: 'Cortical lesions can cause aphasia, motor weakness, sensory loss, visual field defects, or personality changes depending on location. Early recognition is crucial for treatment.',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
    icon: Brain,
    category: 'brain'
  },
  {
    id: 'brainstem',
    name: 'Brainstem',
    description: 'The vital connection between the brain and spinal cord, controlling essential life functions.',
    function: 'Regulates breathing, heart rate, blood pressure, consciousness, and sleep-wake cycles. Contains cranial nerve nuclei and ascending/descending pathways.',
    pathophysiology: 'Brainstem lesions can be life-threatening due to disruption of vital functions. Small lesions can cause significant neurological deficits due to compact anatomy.',
    clinicalSignificance: 'Brainstem strokes, tumours, or trauma require immediate medical attention. Symptoms include altered consciousness, breathing difficulties, and cranial nerve palsies.',
    image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
    icon: Network,
    category: 'brain'
  },
  {
    id: 'cerebellum',
    name: 'Cerebellum',
    description: 'The "little brain" responsible for balance, coordination, and motor learning.',
    function: 'Coordinates voluntary movements, maintains balance and posture, and contributes to motor learning and cognitive functions.',
    pathophysiology: 'Cerebellar dysfunction results in ataxia (uncoordinated movements), tremor, balance problems, and speech difficulties. Does not cause paralysis.',
    clinicalSignificance: 'Cerebellar disorders affect quality of life significantly. Common causes include stroke, tumours, alcohol toxicity, and genetic conditions.',
    image: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop',
    icon: Target,
    category: 'brain'
  },
  {
    id: 'spinal-cord',
    name: 'Spinal Cord',
    description: 'The main pathway for information connecting the brain and peripheral nervous system.',
    function: 'Transmits motor commands from brain to muscles and sensory information from body to brain. Contains reflex circuits for rapid responses.',
    pathophysiology: 'Spinal cord injury can cause paralysis, sensory loss, and autonomic dysfunction below the level of injury. Severity depends on completeness of injury.',
    clinicalSignificance: 'Spinal cord injuries are often permanent. Early recognition and treatment of compression can prevent irreversible damage.',
    image: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=800&h=600&fit=crop',
    icon: Zap,
    category: 'spine'
  },
  {
    id: 'vertebrae',
    name: 'Vertebrae',
    description: 'The bony structures that protect the spinal cord and provide structural support.',
    function: 'Protect spinal cord, support body weight, allow spinal movement, and provide attachment points for muscles and ligaments.',
    pathophysiology: 'Vertebral fractures, degeneration, or misalignment can compress neural structures, causing pain, weakness, and neurological deficits.',
    clinicalSignificance: 'Vertebral pathology is common with aging. Compression fractures, disc degeneration, and spinal stenosis are frequent causes of back pain.',
    image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop',
    icon: Bone,
    category: 'spine'
  },
  {
    id: 'intervertebral-discs',
    name: 'Intervertebral Discs',
    description: 'Fibrocartilaginous cushions between vertebrae that absorb shock and allow spinal movement.',
    function: 'Absorb mechanical stress, distribute loads evenly, and allow spinal flexibility while maintaining stability.',
    pathophysiology: 'Disc degeneration leads to loss of height, reduced shock absorption, and potential herniation. Herniated discs can compress nerve roots.',
    clinicalSignificance: 'Disc problems are a leading cause of back pain and sciatica. Treatment ranges from conservative management to surgical intervention.',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
    icon: Disc,
    category: 'spine'
  }
];

// Common neurological conditions with comprehensive information
export const commonConditions: CommonCondition[] = [
  {
    id: 'stroke',
    name: 'Stroke',
    description: 'Sudden loss of brain function due to interrupted blood supply, requiring immediate medical attention.',
    symptoms: ['Sudden weakness or numbness', 'Speech difficulties', 'Vision problems', 'Severe headache', 'Loss of coordination'],
    causes: ['Blood clot blocking artery', 'Bleeding in brain', 'High blood pressure', 'Atrial fibrillation', 'Atherosclerosis'],
    treatments: ['Emergency clot-busting drugs', 'Mechanical thrombectomy', 'Rehabilitation therapy', 'Blood pressure control'],
    urgency: 'emergency',
    path: '/patient-resources/conditions/stroke'
  },
  {
    id: 'herniated-disc',
    name: 'Herniated Disc',
    description: 'Displacement of disc material that can compress nerve roots, causing pain and neurological symptoms.',
    symptoms: ['Lower back pain', 'Leg pain (sciatica)', 'Numbness or tingling', 'Muscle weakness', 'Difficulty walking'],
    causes: ['Age-related degeneration', 'Sudden lifting or twisting', 'Repetitive stress', 'Genetic factors', 'Obesity'],
    treatments: ['Rest and physiotherapy', 'Pain medications', 'Epidural injections', 'Discectomy surgery'],
    urgency: 'routine',
    path: '/patient-resources/conditions/herniated-disc'
  },
  {
    id: 'spinal-stenosis',
    name: 'Spinal Stenosis',
    description: 'Narrowing of the spinal canal that can compress the spinal cord or nerve roots.',
    symptoms: ['Back pain', 'Leg pain with walking', 'Numbness in legs', 'Weakness', 'Balance problems'],
    causes: ['Age-related changes', 'Arthritis', 'Ligament thickening', 'Disc degeneration', 'Bone spurs'],
    treatments: ['Physiotherapy', 'Pain medications', 'Epidural injections', 'Decompression surgery'],
    urgency: 'routine',
    path: '/patient-resources/conditions/spinal-stenosis'
  }
];

export const healthResources: HealthResource[] = [
  {
    id: 'expert-guide',
    name: 'Expert Guide to Spine & Brain Health',
    description: 'Comprehensive guide covering prevention, maintenance, and optimization of spine and brain health.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/expert-guide-spine-brain-health',
    category: 'Education',
    featured: true
  },
  {
    id: 'individual-programme',
    name: 'Individual Spine Health Programme',
    description: 'Personalised spine health program tailored to your specific needs and condition.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/individual-spine-health-programme',
    category: 'Programs',
    featured: true
  },
  {
    id: 'assessment-tools',
    name: 'Assessment Tools',
    description: 'Comprehensive health assessment tools to evaluate your spine and brain health status.',
    type: 'assessment',
    requiresLogin: true,
    path: '/patient-resources/assessment-tools',
    category: 'Assessment',
    featured: true
  },
  {
    id: 'patient-dashboard',
    name: 'Patient Dashboard',
    description: 'Personal health dashboard to track your progress, appointments, and treatment plans.',
    type: 'program',
    requiresLogin: true,
    path: '/patient-resources/patient-dashboard',
    category: 'Management',
    featured: true
  },
  {
    id: 'spine-health-app',
    name: 'Spine Health App',
    description: 'Mobile application for spine health monitoring, exercises, and progress tracking.',
    type: 'program',
    requiresLogin: false,
    path: '/patient-resources/spine-health-app',
    category: 'Technology',
    featured: false
  },
  {
    id: 'cervical-injury-recovery',
    name: 'Cervical Spine Injury Recovery',
    description: 'Specialised recovery protocols and guidelines for cervical spine injuries.',
    type: 'recovery',
    requiresLogin: false,
    path: '/patient-resources/cervical-spine-injury',
    category: 'Recovery',
    featured: false
  },
  {
    id: 'lifestyle-modifications',
    name: 'Lifestyle Modifications',
    description: 'Evidence-based lifestyle changes to improve spine and brain health outcomes.',
    type: 'education',
    requiresLogin: false,
    path: '/patient-resources/lifestyle-modifications',
    category: 'Education',
    featured: false
  },
  {
    id: 'brain-anatomy',
    name: 'Brain Anatomy',
    description: 'Interactive guide to brain anatomy and function for patient education.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/brain-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'nerve-anatomy',
    name: 'Nerve Anatomy',
    description: 'Comprehensive guide to peripheral and central nervous system anatomy.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/nerve-anatomy',
    category: 'Anatomy',
    featured: false
  },
  {
    id: 'spine-anatomy',
    name: 'Spine Anatomy',
    description: 'Detailed anatomical guide to spinal structure and function.',
    type: 'anatomy',
    requiresLogin: false,
    path: '/patient-resources/spine-anatomy',
    category: 'Anatomy',
    featured: false
  }
];

// Helper functions for resource type styling and icons
export const getTypeColor = (type: string) => {
  switch (type) {
    case 'assessment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'education': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'program': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
    case 'anatomy': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    case 'recovery': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

export const getTypeIcon = (type: string) => {
  switch (type) {
    case 'assessment': return Activity;
    case 'education': return BookOpen;
    case 'program': return Shield;
    case 'anatomy': return Brain;
    case 'recovery': return TrendingUp;
    default: return Brain;
  }
};

// Featured conditions for the conditions library section
export const featuredConditions = [
  {
    name: "Cervical Myelopathy",
    description: "Spinal cord compression in the neck causing weakness and coordination problems",
    urgency: "urgent",
    category: "spine",
    path: "/patient-resources/conditions/cervical-myelopathy",
    image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop"
  },
  {
    name: "Brain Tumour",
    description: "Abnormal growth requiring specialised neurosurgical evaluation and treatment",
    urgency: "urgent",
    category: "brain",
    path: "/patient-resources/conditions/brain-tumour",
    image: "https://images.unsplash.com/photo-**********-0eb30cd8c063?w=400&h=300&fit=crop"
  },
  {
    name: "Herniated Disc",
    description: "Disc displacement causing nerve compression and radiating pain",
    urgency: "routine",
    category: "spine",
    path: "/patient-resources/conditions/herniated-disc",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop"
  },
  {
    name: "Trigeminal Neuralgia",
    description: "Severe facial pain from trigeminal nerve dysfunction",
    urgency: "routine",
    category: "brain",
    path: "/patient-resources/conditions/trigeminal-neuralgia",
    image: "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=300&fit=crop"
  },
  {
    name: "Spinal Stenosis",
    description: "Narrowing of spinal canal causing nerve compression",
    urgency: "routine",
    category: "spine",
    path: "/patient-resources/conditions/spinal-stenosis",
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop"
  },
  {
    name: "Hydrocephalus",
    description: "Fluid accumulation in brain requiring drainage procedures",
    urgency: "urgent",
    category: "brain",
    path: "/patient-resources/conditions/hydrocephalus",
    image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop"
  }
];

export const getUrgencyColor = (urgency: string) => {
  switch (urgency) {
    case 'emergency': return 'bg-error-light/30 border-error/50';
    case 'urgent': return 'bg-info-light/30 border-info/50';
    default: return 'bg-success-light/30 border-success/50';
  }
};

// Health categories for overview section
export const healthCategories = [
  {
    icon: Activity,
    title: 'Assessment Tools',
    description: 'Comprehensive health evaluations and progress tracking'
  },
  {
    icon: BookOpen,
    title: 'Educational Resources',
    description: 'Evidence-based guides and anatomical references'
  },
  {
    icon: Shield,
    title: 'Health Programs',
    description: 'Personalised care plans and wellness programs'
  },
  {
    icon: Brain,
    title: 'Anatomy Guides',
    description: 'Interactive anatomical education and visualization'
  },
  {
    icon: TrendingUp,
    title: 'Recovery Support',
    description: 'Specialised recovery protocols and guidelines'
  },
  {
    icon: Users,
    title: 'Patient Community',
    description: 'Connect with others on similar health journeys'
  }
];

// Lifestyle categories for prevention section
export const lifestyleCategories = [
  {
    icon: Heart,
    title: "Cardiovascular Health",
    description: "Regular aerobic exercise improves blood flow to the brain and spine, reducing risk of stroke and promoting healing.",
    tips: ["30 minutes daily exercise", "Control blood pressure", "Manage cholesterol", "Quit smoking"]
  },
  {
    icon: Brain,
    title: "Cognitive Stimulation",
    description: "Mental challenges and learning new skills promote neuroplasticity and may reduce cognitive decline.",
    tips: ["Learn new skills", "Read regularly", "Solve puzzles", "Social engagement"]
  },
  {
    icon: Bone,
    title: "Spinal Health",
    description: "Proper posture, core strengthening, and ergonomics prevent spinal degeneration and injury.",
    tips: ["Maintain good posture", "Strengthen core muscles", "Ergonomic workspace", "Regular stretching"]
  },
  {
    icon: Shield,
    title: "Injury Prevention",
    description: "Safety measures and protective equipment significantly reduce risk of traumatic brain and spine injuries.",
    tips: ["Wear helmets", "Use seatbelts", "Fall prevention", "Safe lifting techniques"]
  }
];

// Color schemes for lifestyle cards - Using theme colors for consistency
export const lifestyleColorSchemes = [
  {
    bg: 'bg-error-light',
    border: 'border-error/30',
    icon: 'text-foreground',
    title: 'text-foreground',
    accent: 'border-error/50',
    text: 'text-muted-foreground'
  },
  {
    bg: 'bg-info-light',
    border: 'border-info/30',
    icon: 'text-info',
    title: 'text-foreground',
    accent: 'border-info/50',
    text: 'text-muted-foreground'
  },
  {
    bg: 'bg-success-light',
    border: 'border-success/30',
    icon: 'text-success',
    title: 'text-foreground',
    accent: 'border-success/50',
    text: 'text-muted-foreground'
  },
  {
    bg: 'bg-medical-blue-light',
    border: 'border-medical-blue/30',
    icon: 'text-medical-blue',
    title: 'text-foreground',
    accent: 'border-medical-blue/50',
    text: 'text-muted-foreground'
  }
];
