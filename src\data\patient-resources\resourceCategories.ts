
export interface ResourceItem {
  title: string;
  description: string;
  link: string;
  buttonText: string;
}

export interface ResourceCategory {
  id: string;
  title: string;
  icon: LucideIcon;
  resources: ResourceItem[];
}

interface TranslationObject {
  patientResources?: {
    categories?: Record<string, string>;
    resources?: Record<string, string>;
  };
}

export const getResourceCategories = (t: TranslationObject): ResourceCategory[] => [
  {
    id: 'before-visit',
    title: t.patientResources.categories.beforeVisit,
    icon: Calendar,
    resources: [
      {
        title: t.patientResources.resources.newPatientForms,
        description: t.patientResources.resources.newPatientFormsDesc,
        link: "#",
        buttonText: "Download Forms"
      },
      {
        title: t.patientResources.resources.insuranceInfo,
        description: t.patientResources.resources.insuranceInfoDesc,
        link: "#",
        buttonText: "View Details"
      },
      {
        title: t.patientResources.resources.preparingForAppointment,
        description: t.patientResources.resources.preparingForAppointmentDesc,
        link: "#",
        buttonText: "Read More"
      }
    ]
  },
  {
    id: 'conditions-treatments',
    title: t.patientResources.categories.conditionsTreatments,
    icon: Info,
    resources: [
      {
        title: t.patientResources.resources.brainConditions,
        description: t.patientResources.resources.brainConditionsDesc,
        link: "/expertise/image-guided-surgery",
        buttonText: "Learn More"
      },
      {
        title: t.patientResources.resources.spineConditions,
        description: t.patientResources.resources.spineConditionsDesc,
        link: "/expertise/cervical-disc-replacement",
        buttonText: "Learn More"
      },
      {
        title: t.patientResources.resources.minimallyInvasive,
        description: t.patientResources.resources.minimallyInvasiveDesc,
        link: "/expertise/robotic-spine-surgery",
        buttonText: "Learn More"
      }
    ]
  },
  {
    id: 'surgery-info',
    title: t.patientResources.categories.surgeryInfo,
    icon: FileText,
    resources: [
      {
        title: t.patientResources.resources.preSurgeryInstructions,
        description: t.patientResources.resources.preSurgeryInstructionsDesc,
        link: "#",
        buttonText: t.patientResources.viewInstructions
      },
      {
        title: t.patientResources.resources.postSurgeryCare,
        description: t.patientResources.resources.postSurgeryCareDesc,
        link: "#",
        buttonText: "Read More"
      },
      {
        title: t.patientResources.resources.hospitalInfo,
        description: t.patientResources.resources.hospitalInfoDesc,
        link: "#",
        buttonText: t.patientResources.viewHospitals
      }
    ]
  },
  {
    id: 'patient-support',
    title: t.patientResources.categories.patientSupport,
    icon: HelpCircle,
    resources: [
      {
        title: t.patientResources.resources.supportGroups,
        description: t.patientResources.resources.supportGroupsDesc,
        link: "#",
        buttonText: t.patientResources.findSupport
      },
      {
        title: t.patientResources.resources.rehabilitationResources,
        description: t.patientResources.resources.rehabilitationResourcesDesc,
        link: "#",
        buttonText: t.patientResources.viewResources
      },
      {
        title: t.patientResources.resources.mentalHealthSupport,
        description: t.patientResources.resources.mentalHealthSupportDesc,
        link: "#",
        buttonText: "Learn More"
      }
    ]
  }
];
