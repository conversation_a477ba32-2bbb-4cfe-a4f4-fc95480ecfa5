/**
 * Device Context Performance Monitoring
 * Tracks re-render frequency, usage patterns, and optimization opportunities
 */

import { useEffect, useRef, useState, useCallback } from 'react';

export interface DeviceContextMetrics {
  componentName: string;
  renderCount: number;
  lastRender: number;
  averageRenderInterval: number;
  totalRenderTime: number;
  deviceProperties: string[];
  optimizationSuggestions: string[];
}

export interface PerformanceReport {
  timestamp: string;
  totalComponents: number;
  totalRenders: number;
  averageRenderFrequency: number;
  topRenderingComponents: DeviceContextMetrics[];
  optimizationOpportunities: string[];
  performanceScore: number;
}

/**
 * Device Context Performance Monitor
 */
class DeviceContextMonitor {
  private static instance: DeviceContextMonitor;
  private metrics: Map<string, DeviceContextMetrics> = new Map();
  private observers: Array<(report: PerformanceReport) => void> = [];
  private isEnabled: boolean = import.meta.env.DEV;

  static getInstance(): DeviceContextMonitor {
    if (!DeviceContextMonitor.instance) {
      DeviceContextMonitor.instance = new DeviceContextMonitor();
    }
    return DeviceContextMonitor.instance;
  }

  /**
   * Enable or disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled) {
      this.metrics.clear();
    }
  }

  /**
   * Record a component render
   */
  recordRender(
    componentName: string,
    deviceProperties: string[],
    renderTime: number = 0
  ): void {
    if (!this.isEnabled) return;

    const now = Date.now();
    const existing = this.metrics.get(componentName);

    if (existing) {
      const timeSinceLastRender = now - existing.lastRender;
      const newAverageInterval = 
        (existing.averageRenderInterval * existing.renderCount + timeSinceLastRender) / 
        (existing.renderCount + 1);

      this.metrics.set(componentName, {
        ...existing,
        renderCount: existing.renderCount + 1,
        lastRender: now,
        averageRenderInterval: newAverageInterval,
        totalRenderTime: existing.totalRenderTime + renderTime,
        deviceProperties: [...new Set([...existing.deviceProperties, ...deviceProperties])],
        optimizationSuggestions: this.generateOptimizationSuggestions(
          componentName,
          existing.renderCount + 1,
          newAverageInterval,
          deviceProperties
        )
      });
    } else {
      this.metrics.set(componentName, {
        componentName,
        renderCount: 1,
        lastRender: now,
        averageRenderInterval: 0,
        totalRenderTime: renderTime,
        deviceProperties,
        optimizationSuggestions: []
      });
    }

    this.notifyObservers();
  }

  /**
   * Generate optimization suggestions
   */
  private generateOptimizationSuggestions(
    componentName: string,
    renderCount: number,
    averageInterval: number,
    deviceProperties: string[]
  ): string[] {
    const suggestions: string[] = [];

    // High render frequency
    if (renderCount > 50 && averageInterval < 100) {
      suggestions.push('Consider using selective hooks instead of full device context');
    }

    // Using multiple device properties
    if (deviceProperties.length > 3) {
      suggestions.push('Component uses many device properties - consider splitting or using CSS');
    }

    // Frequent re-renders with specific properties
    if (deviceProperties.includes('width') && deviceProperties.includes('height') && renderCount > 20) {
      suggestions.push('Consider using CSS media queries for layout changes');
    }

    // Touch-related properties
    if (deviceProperties.includes('isTouchDevice') && renderCount > 10) {
      suggestions.push('Consider using CSS hover/touch media queries');
    }

    // Orientation changes
    if (deviceProperties.includes('orientation') && renderCount > 15) {
      suggestions.push('Consider using CSS orientation media queries');
    }

    return suggestions;
  }

  /**
   * Generate performance report
   */
  generateReport(): PerformanceReport {
    const metrics = Array.from(this.metrics.values());
    const totalRenders = metrics.reduce((sum, m) => sum + m.renderCount, 0);
    const totalComponents = metrics.length;
    
    // Calculate average render frequency (renders per second)
    const averageRenderFrequency = totalComponents > 0 
      ? metrics.reduce((sum, m) => sum + (1000 / Math.max(m.averageRenderInterval, 1)), 0) / totalComponents
      : 0;

    // Get top rendering components
    const topRenderingComponents = metrics
      .sort((a, b) => b.renderCount - a.renderCount)
      .slice(0, 5);

    // Collect all optimization opportunities
    const optimizationOpportunities = [
      ...new Set(metrics.flatMap(m => m.optimizationSuggestions))
    ];

    // Calculate performance score (0-100)
    let performanceScore = 100;
    
    // Deduct points for high render frequency
    if (averageRenderFrequency > 10) performanceScore -= 20;
    if (averageRenderFrequency > 20) performanceScore -= 30;
    
    // Deduct points for components with many renders
    const highRenderComponents = metrics.filter(m => m.renderCount > 100);
    performanceScore -= highRenderComponents.length * 10;
    
    // Deduct points for optimization opportunities
    performanceScore -= optimizationOpportunities.length * 5;
    
    performanceScore = Math.max(0, performanceScore);

    return {
      timestamp: new Date().toISOString(),
      totalComponents,
      totalRenders,
      averageRenderFrequency,
      topRenderingComponents,
      optimizationOpportunities,
      performanceScore
    };
  }

  /**
   * Subscribe to performance updates
   */
  subscribe(callback: (report: PerformanceReport) => void): () => void {
    this.observers.push(callback);
    return () => {
      const index = this.observers.indexOf(callback);
      if (index > -1) {
        this.observers.splice(index, 1);
      }
    };
  }

  /**
   * Notify observers of updates
   */
  private notifyObservers(): void {
    const report = this.generateReport();
    this.observers.forEach(callback => callback(report));
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    this.notifyObservers();
  }

  /**
   * Get metrics for a specific component
   */
  getComponentMetrics(componentName: string): DeviceContextMetrics | undefined {
    return this.metrics.get(componentName);
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): DeviceContextMetrics[] {
    return Array.from(this.metrics.values());
  }
}

// Export singleton instance
export const deviceContextMonitor = DeviceContextMonitor.getInstance();

/**
 * Hook to monitor device context usage in components
 */
export function useDeviceContextMonitor(
  componentName: string,
  deviceProperties: string[] = []
): void {
  const renderCountRef = useRef(0);
  const renderStartRef = useRef(0);

  useEffect(() => {
    renderStartRef.current = performance.now();
    renderCountRef.current++;
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartRef.current;
    deviceContextMonitor.recordRender(componentName, deviceProperties, renderTime);
  });
}

/**
 * Hook to track device context performance
 */
export function useDeviceContextPerformance(): {
  report: PerformanceReport | null;
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
  clearMetrics: () => void;
} {
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const startMonitoring = useCallback(() => {
    deviceContextMonitor.setEnabled(true);
    setIsMonitoring(true);
    
    const unsubscribe = deviceContextMonitor.subscribe(setReport);
    return unsubscribe;
  }, []);

  const stopMonitoring = useCallback(() => {
    deviceContextMonitor.setEnabled(false);
    setIsMonitoring(false);
    setReport(null);
  }, []);

  const clearMetrics = useCallback(() => {
    deviceContextMonitor.clear();
  }, []);

  useEffect(() => {
    if (import.meta.env.DEV) {
      const unsubscribe = startMonitoring();
      return unsubscribe;
    }
  }, [startMonitoring]);

  return {
    report,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    clearMetrics
  };
}

/**
 * Development-only performance dashboard component
 */
export function DeviceContextPerformanceDashboard(): JSX.Element | null {
  const { report, isMonitoring: _isMonitoring, startMonitoring, stopMonitoring, clearMetrics } =
    useDeviceContextPerformance();

  if (!import.meta.env.DEV) {
    return null;
  }

  if (!report) {
    return (
      <div className="fixed bottom-4 right-4 bg-card border border-border rounded-lg p-4 shadow-lg">
        <h3 className="font-semibold mb-2">Device Context Monitor</h3>
        <p className="text-sm text-muted-foreground mb-2">No data available</p>
        <button
          onClick={startMonitoring}
          className="px-3 py-1 bg-primary text-primary-foreground rounded text-sm"
        >
          Start Monitoring
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-card border border-border rounded-lg p-4 shadow-lg max-w-sm">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold">Device Context Monitor</h3>
        <div className="flex gap-1">
          <button
            onClick={clearMetrics}
            className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs"
          >
            Clear
          </button>
          <button
            onClick={stopMonitoring}
            className="px-2 py-1 bg-destructive text-destructive-foreground rounded text-xs"
          >
            Stop
          </button>
        </div>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Performance Score:</span>
          <span className={`font-semibold ${
            report.performanceScore > 80 ? 'text-green-600' :
            report.performanceScore > 60 ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {report.performanceScore}/100
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Components:</span>
          <span>{report.totalComponents}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Total Renders:</span>
          <span>{report.totalRenders}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Avg Frequency:</span>
          <span>{report.averageRenderFrequency.toFixed(1)}/s</span>
        </div>
        
        {report.optimizationOpportunities.length > 0 && (
          <div className="mt-2 pt-2 border-t border-border">
            <p className="font-medium text-yellow-600 mb-1">Optimizations:</p>
            <ul className="text-xs space-y-1">
              {report.optimizationOpportunities.slice(0, 3).map((suggestion, index) => (
                <li key={index} className="text-muted-foreground">
                  • {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
