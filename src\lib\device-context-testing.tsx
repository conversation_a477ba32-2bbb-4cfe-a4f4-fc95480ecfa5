/**
 * Device Context Testing Framework
 * Comprehensive testing utilities for device context performance and optimization validation
 */

import { renderHook, act } from '@testing-library/react-hooks';
import React, { ReactElement } from 'react';

import { DeviceProvider } from '@/contexts/DeviceContext';

export interface DeviceTestConfig {
  width?: number;
  height?: number;
  userAgent?: string;
  touchSupport?: boolean;
  hoverSupport?: boolean;
  debounceDelay?: number;
  enablePerformanceMonitoring?: boolean;
}

export interface PerformanceTestResult {
  componentName: string;
  renderCount: number;
  averageRenderTime: number;
  totalRenderTime: number;
  reRenderTriggers: string[];
  optimizationScore: number;
  suggestions: string[];
}

export interface DeviceContextTestSuite {
  testName: string;
  results: PerformanceTestResult[];
  overallScore: number;
  summary: {
    totalComponents: number;
    totalRenders: number;
    averageOptimizationScore: number;
    criticalIssues: string[];
    recommendations: string[];
  };
}

/**
 * Device Context Test Utilities
 */
export class DeviceContextTester {
  private static instance: DeviceContextTester;
  private testResults: Map<string, PerformanceTestResult> = new Map();
  private currentTestSuite: string = '';

  static getInstance(): DeviceContextTester {
    if (!DeviceContextTester.instance) {
      DeviceContextTester.instance = new DeviceContextTester();
    }
    return DeviceContextTester.instance;
  }

  /**
   * Create a test wrapper with device context
   */
  createTestWrapper(config: DeviceTestConfig = {}) {
    const defaultConfig: DeviceTestConfig = {
      width: 1024,
      height: 768,
      touchSupport: false,
      hoverSupport: true,
      debounceDelay: 0, // Disable debouncing in tests
      enablePerformanceMonitoring: true,
      ...config
    };

    // Mock window properties
    this.mockWindowProperties(defaultConfig);

    return ({ children }: { children: React.ReactNode }) => (
      <DeviceProvider
        debounceDelay={defaultConfig.debounceDelay}
        enablePerformanceMonitoring={defaultConfig.enablePerformanceMonitoring}
      >
        {children}
      </DeviceProvider>
    );
  }

  /**
   * Mock window properties for testing
   */
  private mockWindowProperties(config: DeviceTestConfig): void {
    // Mock window dimensions
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: config.width,
    });

    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: config.height,
    });

    // Mock user agent
    if (config.userAgent) {
      Object.defineProperty(navigator, 'userAgent', {
        writable: true,
        configurable: true,
        value: config.userAgent,
      });
    }

    // Mock touch support
    if (config.touchSupport !== undefined) {
      Object.defineProperty(window, 'ontouchstart', {
        writable: true,
        configurable: true,
        value: config.touchSupport ? {} : undefined,
      });
    }

    // Mock hover support
    if (config.hoverSupport !== undefined) {
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(hover: hover)' ? config.hoverSupport : false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));
    }
  }

  /**
   * Test component re-render performance
   */
  async testComponentPerformance<T>(
    hookOrComponent: () => T | ReactElement,
    testConfig: DeviceTestConfig = {},
    testName: string = 'UnnamedTest'
  ): Promise<PerformanceTestResult> {
    const wrapper = this.createTestWrapper(testConfig);
    let renderCount = 0;
    let totalRenderTime = 0;
    const renderTimes: number[] = [];
    const reRenderTriggers: string[] = [];

    // Test hook performance
    if (typeof hookOrComponent === 'function') {
      const { result: _result, rerender } = renderHook(() => {
        const startTime = performance.now();
        renderCount++;
        const hookResult = hookOrComponent();
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        renderTimes.push(renderTime);
        totalRenderTime += renderTime;
        return hookResult;
      }, { wrapper });

      // Test various device changes
      await this.simulateDeviceChanges(rerender, reRenderTriggers);
    }

    const averageRenderTime = renderTimes.length > 0 
      ? renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length 
      : 0;

    const optimizationScore = this.calculateOptimizationScore(
      renderCount,
      averageRenderTime,
      reRenderTriggers
    );

    const suggestions = this.generateOptimizationSuggestions(
      renderCount,
      averageRenderTime,
      reRenderTriggers
    );

    const result: PerformanceTestResult = {
      componentName: testName,
      renderCount,
      averageRenderTime,
      totalRenderTime,
      reRenderTriggers,
      optimizationScore,
      suggestions
    };

    this.testResults.set(testName, result);
    return result;
  }

  /**
   * Simulate various device changes
   */
  private async simulateDeviceChanges(
    rerender: () => void,
    reRenderTriggers: string[]
  ): Promise<void> {
    const changes = [
      { width: 768, height: 1024, trigger: 'mobile-resize' },
      { width: 1024, height: 768, trigger: 'tablet-resize' },
      { width: 1920, height: 1080, trigger: 'desktop-resize' },
      { width: 375, height: 667, trigger: 'mobile-portrait' },
      { width: 667, height: 375, trigger: 'mobile-landscape' },
    ];

    for (const change of changes) {
      act(() => {
        window.innerWidth = change.width;
        window.innerHeight = change.height;
        window.dispatchEvent(new Event('resize'));
        reRenderTriggers.push(change.trigger);
      });
      
      rerender();
      
      // Small delay to allow for debouncing
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * Calculate optimization score (0-100)
   */
  private calculateOptimizationScore(
    renderCount: number,
    averageRenderTime: number,
    reRenderTriggers: string[]
  ): number {
    let score = 100;

    // Deduct points for excessive renders
    if (renderCount > 10) score -= 20;
    if (renderCount > 20) score -= 30;

    // Deduct points for slow render times
    if (averageRenderTime > 5) score -= 15;
    if (averageRenderTime > 10) score -= 25;

    // Deduct points for unnecessary re-renders
    const unnecessaryRenders = reRenderTriggers.length - 3; // Allow 3 meaningful changes
    if (unnecessaryRenders > 0) {
      score -= unnecessaryRenders * 10;
    }

    return Math.max(0, score);
  }

  /**
   * Generate optimization suggestions
   */
  private generateOptimizationSuggestions(
    renderCount: number,
    averageRenderTime: number,
    reRenderTriggers: string[]
  ): string[] {
    const suggestions: string[] = [];

    if (renderCount > 15) {
      suggestions.push('Consider using selective hooks instead of full device context');
    }

    if (averageRenderTime > 5) {
      suggestions.push('Optimize component rendering with React.memo or useMemo');
    }

    if (reRenderTriggers.length > 5) {
      suggestions.push('Reduce unnecessary re-renders with better memoization');
    }

    if (reRenderTriggers.includes('mobile-resize') && reRenderTriggers.includes('tablet-resize')) {
      suggestions.push('Consider using CSS media queries for layout changes');
    }

    return suggestions;
  }

  /**
   * Run comprehensive test suite
   */
  async runTestSuite(
    tests: Array<{
      name: string;
      test: () => unknown;
      config?: DeviceTestConfig;
    }>,
    suiteName: string = 'DeviceContextTestSuite'
  ): Promise<DeviceContextTestSuite> {
    this.currentTestSuite = suiteName;
    this.testResults.clear();

    const results: PerformanceTestResult[] = [];

    for (const testCase of tests) {
      try {
        const result = await this.testComponentPerformance(
          testCase.test,
          testCase.config,
          testCase.name
        );
        results.push(result);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.error(`Test failed: ${testCase.name}`, error);
        }
      }
    }

    return this.generateTestSuiteReport(suiteName, results);
  }

  /**
   * Generate test suite report
   */
  private generateTestSuiteReport(
    suiteName: string,
    results: PerformanceTestResult[]
  ): DeviceContextTestSuite {
    const totalComponents = results.length;
    const totalRenders = results.reduce((sum, r) => sum + r.renderCount, 0);
    const averageOptimizationScore = totalComponents > 0
      ? results.reduce((sum, r) => sum + r.optimizationScore, 0) / totalComponents
      : 0;

    const criticalIssues: string[] = [];
    const recommendations: string[] = [];

    results.forEach(result => {
      if (result.optimizationScore < 50) {
        criticalIssues.push(`${result.componentName}: Low optimization score (${result.optimizationScore})`);
      }
      recommendations.push(...result.suggestions);
    });

    // Remove duplicate recommendations
    const uniqueRecommendations = [...new Set(recommendations)];

    return {
      testName: suiteName,
      results,
      overallScore: averageOptimizationScore,
      summary: {
        totalComponents,
        totalRenders,
        averageOptimizationScore,
        criticalIssues,
        recommendations: uniqueRecommendations
      }
    };
  }

  /**
   * Generate test report
   */
  generateReport(testSuite: DeviceContextTestSuite): string {
    const report = [
      `# Device Context Test Report: ${testSuite.testName}`,
      `Generated: ${new Date().toISOString()}`,
      '',
      '## Summary',
      `- Overall Score: ${testSuite.overallScore.toFixed(1)}/100`,
      `- Total Components: ${testSuite.summary.totalComponents}`,
      `- Total Renders: ${testSuite.summary.totalRenders}`,
      `- Average Optimization Score: ${testSuite.summary.averageOptimizationScore.toFixed(1)}`,
      '',
      '## Component Results',
      ...testSuite.results.map(result => [
        `### ${result.componentName}`,
        `- Optimization Score: ${result.optimizationScore}/100`,
        `- Render Count: ${result.renderCount}`,
        `- Average Render Time: ${result.averageRenderTime.toFixed(2)}ms`,
        `- Re-render Triggers: ${result.reRenderTriggers.length}`,
        result.suggestions.length > 0 ? '- Suggestions:' : '',
        ...result.suggestions.map(s => `  - ${s}`),
        ''
      ]).flat(),
      ''
    ];

    if (testSuite.summary.criticalIssues.length > 0) {
      report.push(
        '## Critical Issues',
        ...testSuite.summary.criticalIssues.map(issue => `- ${issue}`),
        ''
      );
    }

    if (testSuite.summary.recommendations.length > 0) {
      report.push(
        '## Recommendations',
        ...testSuite.summary.recommendations.map(rec => `- ${rec}`),
        ''
      );
    }

    return report.join('\n');
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.testResults.clear();
  }

  /**
   * Get test results
   */
  getResults(): Map<string, PerformanceTestResult> {
    return new Map(this.testResults);
  }
}

// Export singleton instance
export const deviceContextTester = DeviceContextTester.getInstance();

/**
 * Utility functions for common test scenarios
 */

// Test mobile responsiveness
export const testMobileResponsiveness = (component: () => unknown) => {
  return deviceContextTester.testComponentPerformance(
    component,
    { width: 375, height: 667, touchSupport: true, hoverSupport: false },
    'MobileResponsiveness'
  );
};

// Test tablet responsiveness
export const testTabletResponsiveness = (component: () => unknown) => {
  return deviceContextTester.testComponentPerformance(
    component,
    { width: 768, height: 1024, touchSupport: true, hoverSupport: false },
    'TabletResponsiveness'
  );
};

// Test desktop responsiveness
export const testDesktopResponsiveness = (component: () => unknown) => {
  return deviceContextTester.testComponentPerformance(
    component,
    { width: 1920, height: 1080, touchSupport: false, hoverSupport: true },
    'DesktopResponsiveness'
  );
};

// Test hook performance
export const testHookPerformance = (hook: () => unknown, testName: string) => {
  return deviceContextTester.testComponentPerformance(hook, {}, testName);
};

// Create test wrapper for Jest tests
export const createDeviceTestWrapper = (config: DeviceTestConfig = {}) => {
  return deviceContextTester.createTestWrapper(config);
};

/**
 * Example test utilities
 */

// Benchmark selective hooks vs full context
export const benchmarkHookPerformance = async () => {
  const results = await deviceContextTester.runTestSuite([
    {
      name: 'useIsMobile (selective)',
      test: () => {
        // This would be imported from your actual hooks
        // const isMobile = useIsMobile();
        // return isMobile;
        return true; // Placeholder
      }
    },
    {
      name: 'useDeviceDetection (full context)',
      test: () => {
        // This would be imported from your actual hooks
        // const { isMobile } = useDeviceDetection();
        // return isMobile;
        return true; // Placeholder
      }
    }
  ], 'HookPerformanceBenchmark');

  return results;
};

// Test component optimization
export const testComponentOptimization = async (
  component: () => unknown,
  componentName: string
) => {
  const unoptimized = await deviceContextTester.testComponentPerformance(
    component,
    {},
    `${componentName}_Unoptimized`
  );

  // Test with memoization (this would be the memoized version)
  const optimized = await deviceContextTester.testComponentPerformance(
    component, // In real tests, this would be React.memo(component)
    {},
    `${componentName}_Optimized`
  );

  return { unoptimized, optimized };
};
