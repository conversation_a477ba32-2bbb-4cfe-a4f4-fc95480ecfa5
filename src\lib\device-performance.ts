/**
 * Advanced throttling and debouncing utilities for device context
 * Provides sophisticated performance optimization mechanisms
 */

export interface ThrottleOptions {
  delay: number;
  leading?: boolean;
  trailing?: boolean;
  maxWait?: number;
}

export interface DebounceOptions {
  delay: number;
  leading?: boolean;
  trailing?: boolean;
  maxWait?: number;
}

export interface PerformanceMetrics {
  callCount: number;
  executionCount: number;
  averageDelay: number;
  lastExecution: number;
  droppedCalls: number;
}

/**
 * Advanced throttle function with performance monitoring
 */
export function createAdvancedThrottle<T extends (...args: unknown[]) => unknown>(
  func: T,
  options: ThrottleOptions
): T & { metrics: () => PerformanceMetrics; cancel: () => void; flush: () => void } {
  const { delay, leading = true, trailing = true, maxWait } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let maxTimeoutId: NodeJS.Timeout | null = null;
  let lastCallTime = 0;
  let lastExecTime = 0;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: unknown = null;
  
  // Performance metrics
  const metrics: PerformanceMetrics = {
    callCount: 0,
    executionCount: 0,
    averageDelay: 0,
    lastExecution: 0,
    droppedCalls: 0
  };

  function execute() {
    if (lastArgs !== null) {
      const now = Date.now();
      const result = func.apply(lastThis, lastArgs);
      
      // Update metrics
      metrics.executionCount++;
      metrics.lastExecution = now;
      metrics.averageDelay = (metrics.averageDelay * (metrics.executionCount - 1) + (now - lastCallTime)) / metrics.executionCount;
      
      lastExecTime = now;
      lastArgs = null;
      lastThis = null;
      
      return result;
    }
  }

  function cancel() {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }
    lastArgs = null;
    lastThis = null;
  }

  function flush() {
    if (timeoutId) {
      cancel();
      return execute();
    }
  }

  const throttled = function (this: unknown, ...args: Parameters<T>) {
    const now = Date.now();
    lastCallTime = now;
    lastArgs = args;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    lastThis = this;
    
    metrics.callCount++;

    const timeSinceLastExec = now - lastExecTime;
    
    if (lastExecTime === 0 && leading) {
      // First call with leading edge
      lastExecTime = now;
      return execute();
    }

    if (timeSinceLastExec >= delay) {
      // Enough time has passed, execute immediately
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      return execute();
    }

    // Schedule execution
    if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        timeoutId = null;
        execute();
      }, delay - timeSinceLastExec);
    } else if (!trailing) {
      metrics.droppedCalls++;
    }

    // Handle maxWait
    if (maxWait && !maxTimeoutId) {
      maxTimeoutId = setTimeout(() => {
        maxTimeoutId = null;
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        execute();
      }, maxWait);
    }
  } as T & { metrics: () => PerformanceMetrics; cancel: () => void; flush: () => void };

  throttled.metrics = () => ({ ...metrics });
  throttled.cancel = cancel;
  throttled.flush = flush;

  return throttled;
}

/**
 * Advanced debounce function with performance monitoring
 */
export function createAdvancedDebounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  options: DebounceOptions
): T & { metrics: () => PerformanceMetrics; cancel: () => void; flush: () => void } {
  const { delay, leading = false, trailing = true, maxWait } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let maxTimeoutId: NodeJS.Timeout | null = null;
  let lastCallTime = 0;
  let lastExecTime = 0;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: unknown = null;
  
  // Performance metrics
  const metrics: PerformanceMetrics = {
    callCount: 0,
    executionCount: 0,
    averageDelay: 0,
    lastExecution: 0,
    droppedCalls: 0
  };

  function execute() {
    if (lastArgs !== null) {
      const now = Date.now();
      const result = func.apply(lastThis, lastArgs);
      
      // Update metrics
      metrics.executionCount++;
      metrics.lastExecution = now;
      metrics.averageDelay = (metrics.averageDelay * (metrics.executionCount - 1) + (now - lastCallTime)) / metrics.executionCount;
      
      lastExecTime = now;
      lastArgs = null;
      lastThis = null;
      
      return result;
    }
  }

  function cancel() {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }
    lastArgs = null;
    lastThis = null;
  }

  function flush() {
    if (timeoutId || lastArgs) {
      cancel();
      return execute();
    }
  }

  const debounced = function (this: unknown, ...args: Parameters<T>) {
    const now = Date.now();
    lastCallTime = now;
    lastArgs = args;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    lastThis = this;
    
    metrics.callCount++;

    const isFirstCall = lastExecTime === 0;
    
    if (isFirstCall && leading) {
      // First call with leading edge
      lastExecTime = now;
      return execute();
    }

    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Set new timeout for trailing execution
    if (trailing) {
      timeoutId = setTimeout(() => {
        timeoutId = null;
        execute();
      }, delay);
    } else if (!leading) {
      metrics.droppedCalls++;
    }

    // Handle maxWait
    if (maxWait && !maxTimeoutId && !isFirstCall) {
      maxTimeoutId = setTimeout(() => {
        maxTimeoutId = null;
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        execute();
      }, maxWait);
    }
  } as T & { metrics: () => PerformanceMetrics; cancel: () => void; flush: () => void };

  debounced.metrics = () => ({ ...metrics });
  debounced.cancel = cancel;
  debounced.flush = flush;

  return debounced;
}

/**
 * Adaptive debouncing based on device performance
 */
export function createAdaptiveDebounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  baseDelay: number = 100
): T & { metrics: () => PerformanceMetrics; cancel: () => void; flush: () => void } {
  // Detect device performance characteristics
  const getAdaptiveDelay = () => {
    if (typeof window === 'undefined') return baseDelay;
    
    // Check for performance hints
    const connection = (navigator as Navigator & { connection?: { effectiveType?: string } }).connection;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const deviceMemory = (navigator as Navigator & { deviceMemory?: number }).deviceMemory || 4;
    
    let multiplier = 1;
    
    // Adjust based on network speed
    if (connection) {
      switch (connection.effectiveType) {
        case 'slow-2g':
        case '2g':
          multiplier *= 2;
          break;
        case '3g':
          multiplier *= 1.5;
          break;
        case '4g':
          multiplier *= 0.8;
          break;
      }
    }
    
    // Adjust based on hardware
    if (hardwareConcurrency < 4) multiplier *= 1.5;
    if (deviceMemory < 4) multiplier *= 1.3;
    
    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      multiplier *= 1.2;
    }
    
    return Math.max(16, Math.min(500, baseDelay * multiplier));
  };

  return createAdvancedDebounce(func, {
    delay: getAdaptiveDelay(),
    trailing: true,
    maxWait: baseDelay * 3
  });
}

/**
 * Performance monitoring utilities
 */
export class DevicePerformanceMonitor {
  private static instance: DevicePerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: Array<(metrics: Map<string, PerformanceMetrics>) => void> = [];

  static getInstance(): DevicePerformanceMonitor {
    if (!DevicePerformanceMonitor.instance) {
      DevicePerformanceMonitor.instance = new DevicePerformanceMonitor();
    }
    return DevicePerformanceMonitor.instance;
  }

  registerFunction(name: string, func: { metrics: () => PerformanceMetrics }) {
    const updateMetrics = () => {
      this.metrics.set(name, func.metrics());
      this.notifyObservers();
    };

    // Update metrics periodically
    setInterval(updateMetrics, 1000);
    updateMetrics();
  }

  subscribe(callback: (metrics: Map<string, PerformanceMetrics>) => void) {
    this.observers.push(callback);
    return () => {
      const index = this.observers.indexOf(callback);
      if (index > -1) {
        this.observers.splice(index, 1);
      }
    };
  }

  private notifyObservers() {
    this.observers.forEach(callback => callback(new Map(this.metrics)));
  }

  getMetrics(): Map<string, PerformanceMetrics> {
    return new Map(this.metrics);
  }

  getReport(): string {
    const report = ['Device Performance Report', '='.repeat(30)];
    
    this.metrics.forEach((metrics, name) => {
      report.push(`\n${name}:`);
      report.push(`  Calls: ${metrics.callCount}`);
      report.push(`  Executions: ${metrics.executionCount}`);
      report.push(`  Dropped: ${metrics.droppedCalls}`);
      report.push(`  Avg Delay: ${metrics.averageDelay.toFixed(2)}ms`);
      report.push(`  Efficiency: ${((metrics.executionCount / metrics.callCount) * 100).toFixed(1)}%`);
    });
    
    return report.join('\n');
  }
}
