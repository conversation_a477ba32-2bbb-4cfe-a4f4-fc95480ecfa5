/**
 * Centralised Logging Utility
 * Replaces duplicate console logging patterns throughout the codebase
 * Provides consistent logging with environment-aware behaviour
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogContext {
  component?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  [key: string]: unknown;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: LogContext;
  timestamp: string;
  error?: Error;
}

class Logger {
  private isDevelopment: boolean;
  private isProduction: boolean;
  private logHistory: LogEntry[] = [];
  private maxHistorySize = 1000;

  constructor() {
    this.isDevelopment = import.meta.env.DEV;
    this.isProduction = import.meta.env.PROD;
  }

  /**
   * Log debug information (only in development)
   */
  debug(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      this.log('debug', message, context);
      if (import.meta.env.DEV) {
        console.debug(this.formatMessage('DEBUG', message), context);
      }
    }
  }

  /**
   * Log general information
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
    if (this.isDevelopment && import.meta.env.DEV) {
      console.info(this.formatMessage('INFO', message), context);
    }
  }

  /**
   * Log warnings
   */
  warn(message: string, context?: LogContext, error?: Error): void {
    this.log('warn', message, context, error);
    if (this.isDevelopment && import.meta.env.DEV) {
      console.warn(this.formatMessage('WARN', message), context, error);
    }
  }

  /**
   * Log errors
   */
  error(message: string, context?: LogContext, error?: Error): void {
    this.log('error', message, context, error);
    if (this.isDevelopment && import.meta.env.DEV) {
      console.error(this.formatMessage('ERROR', message), context, error);
    }
  }

  /**
   * Log component rendering errors
   */
  componentError(componentName: string, error: Error, context?: LogContext): void {
    const message = `Component rendering failed: ${componentName}`;
    const enhancedContext = {
      ...context,
      component: componentName,
      action: 'render',
      errorMessage: error.message,
      errorStack: error.stack,
    };
    this.error(message, enhancedContext, error);
  }

  /**
   * Log API errors
   */
  apiError(endpoint: string, error: Error, context?: LogContext): void {
    const message = `API request failed: ${endpoint}`;
    const enhancedContext = {
      ...context,
      action: 'api_request',
      endpoint,
      errorMessage: error.message,
    };
    this.error(message, enhancedContext, error);
  }

  /**
   * Log navigation events
   */
  navigation(from: string, to: string, context?: LogContext): void {
    const message = `Navigation: ${from} → ${to}`;
    const enhancedContext = {
      ...context,
      action: 'navigation',
      from,
      to,
    };
    this.info(message, enhancedContext);
  }

  /**
   * Log user interactions
   */
  userAction(action: string, element: string, context?: LogContext): void {
    const message = `User action: ${action} on ${element}`;
    const enhancedContext = {
      ...context,
      action: 'user_interaction',
      userAction: action,
      element,
    };
    this.info(message, enhancedContext);
  }

  /**
   * Log performance metrics
   */
  performance(metric: string, value: number, unit: string, context?: LogContext): void {
    const message = `Performance: ${metric} = ${value}${unit}`;
    const enhancedContext = {
      ...context,
      action: 'performance',
      metric,
      value,
      unit,
    };
    this.info(message, enhancedContext);
  }

  /**
   * Log test results
   */
  test(testName: string, result: 'pass' | 'fail' | 'skip', context?: LogContext): void {
    const message = `Test ${result.toUpperCase()}: ${testName}`;
    const enhancedContext = {
      ...context,
      action: 'test',
      testName,
      result,
    };
    
    if (result === 'fail') {
      this.error(message, enhancedContext);
    } else {
      this.info(message, enhancedContext);
    }
  }

  /**
   * Get recent log entries
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logHistory.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logHistory.filter(entry => entry.level === level);
  }

  /**
   * Clear log history
   */
  clearHistory(): void {
    this.logHistory = [];
  }

  /**
   * Export logs for debugging
   */
  exportLogs(): string {
    return JSON.stringify(this.logHistory, null, 2);
  }

  private log(level: LogLevel, message: string, context?: LogContext, error?: Error): void {
    const entry: LogEntry = {
      level,
      message,
      context,
      timestamp: new Date().toISOString(),
      error,
    };

    this.logHistory.push(entry);

    // Maintain history size limit
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory = this.logHistory.slice(-this.maxHistorySize);
    }
  }

  private formatMessage(level: string, message: string): string {
    const timestamp = new Date().toLocaleTimeString();
    return `[${timestamp}] ${level}: ${message}`;
  }
}

// Create singleton instance
export const logger = new Logger();

// Convenience functions for common patterns found in the codebase
export const logComponentError = (componentName: string, error: Error, context?: LogContext) => {
  logger.componentError(componentName, error, context);
};

export const logApiError = (endpoint: string, error: Error, context?: LogContext) => {
  logger.apiError(endpoint, error, context);
};

export const logUserAction = (action: string, element: string, context?: LogContext) => {
  logger.userAction(action, element, context);
};

export const logNavigation = (from: string, to: string, context?: LogContext) => {
  logger.navigation(from, to, context);
};

export const logPerformance = (metric: string, value: number, unit: string, context?: LogContext) => {
  logger.performance(metric, value, unit, context);
};

export const logTest = (testName: string, result: 'pass' | 'fail' | 'skip', context?: LogContext) => {
  logger.test(testName, result, context);
};

// Development-only logging helpers
export const devLog = {
  debug: (message: string, context?: LogContext) => logger.debug(message, context),
  info: (message: string, context?: LogContext) => logger.info(message, context),
  warn: (message: string, context?: LogContext) => logger.warn(message, context),
  error: (message: string, context?: LogContext, error?: Error) => logger.error(message, context, error),
};

export default logger;
