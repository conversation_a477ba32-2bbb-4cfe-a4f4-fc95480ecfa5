import { ArrowRight, CheckCircle } from 'lucide-react';
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

/**
 * Neurosurgical Intelligence Hub - Patient Resources
 * World-leading patient empowerment platform combining expert knowledge,
 * interactive tools, and personalised care pathways
 */

const PatientResources: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Assessment and support tools
  const assessmentTools = [
    {
      id: 'symptom-assessment',
      title: 'Symptom Assessment Tool',
      description: 'Structured assessment to help evaluate your symptoms and determine appropriate care.',
      icon: Brain,
      type: 'assessment',
      timeToComplete: '5-10 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Symptom Analysis', 'Severity Assessment', 'Care Recommendations', 'Specialist Guidance']
    },
    {
      id: 'treatment-information',
      title: 'Treatment Information Guide',
      description: 'Comprehensive information about treatment options and expected outcomes.',
      icon: Calculator,
      type: 'information',
      timeToComplete: '10-15 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Treatment Options', 'Outcome Information', 'Recovery Expectations', 'Risk Factors']
    },
    {
      id: 'decision-guide',
      title: 'Treatment Decision Guide',
      description: 'Structured approach to understanding your treatment choices and considerations.',
      icon: Target,
      type: 'decision',
      timeToComplete: '15-20 min',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Decision Framework', 'Key Considerations', 'Expert Guidance', 'Personalised Information']
    },
    {
      id: 'recovery-support',
      title: 'Recovery Support Tools',
      description: 'Resources and tools to support your recovery and ongoing health management.',
      icon: TrendingUp,
      type: 'support',
      timeToComplete: 'Ongoing',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Recovery Resources', 'Progress Monitoring', 'Support Networks', 'Health Management']
    }
  ];

  // Specialised condition areas
  const conditionAreas = [
    {
      id: 'brain-conditions',
      title: 'Brain Conditions',
      description: 'Comprehensive information about brain tumours, neurological disorders, and treatment approaches.',
      icon: Brain,
      link: '/patient-resources/brain-conditions',
      stats: '15 Conditions',
      highlight: 'Neurosurgical Expertise',
      features: ['Tumour Management', 'Surgical Options', 'Recovery Support', 'Risk Information'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    },
    {
      id: 'spine-conditions',
      title: 'Spine Conditions',
      description: 'Complete information about spine health, conditions, and modern treatment options.',
      icon: Bone,
      link: '/patient-resources/spine-conditions',
      stats: '15 Conditions',
      highlight: 'Minimally Invasive Techniques',
      features: ['Advanced Surgery', 'Motion Preservation', 'Pain Management', 'Recovery Support'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    },
    {
      id: 'nerve-conditions',
      title: 'Peripheral Nerve Conditions',
      description: 'Specialised information about peripheral nerve disorders and microsurgical treatments.',
      icon: Network,
      link: '/patient-resources/peripheral-nerve-conditions',
      stats: '8 Conditions',
      highlight: 'Microsurgical Expertise',
      features: ['Nerve Repair', 'Function Restoration', 'Pain Relief', 'Mobility Improvement'],
      outcomes: 'Excellent outcomes',
      experience: 'Extensive experience'
    }
  ];

  // Additional support resources
  const supportResources = [
    {
      category: 'Exercise & Rehabilitation',
      icon: Dumbbell,
      title: 'Exercise Library',
      description: 'Evidence-based exercise programmes designed for spine health and safe rehabilitation.',
      link: '/patient-resources/exercise-library',
      features: ['6 Specialised Programmes', 'Video Demonstrations', 'Progress Tracking', 'Safety Guidelines'],
      highlight: 'Spine-Safe Exercises'
    },
    {
      category: 'Health & Wellness',
      icon: Heart,
      title: 'Brain and Spine Health',
      description: 'Comprehensive health information covering anatomy, lifestyle, and recovery support.',
      link: '/patient-resources/brain-and-spine-health',
      features: ['Lifestyle Information', 'Nutrition Guidance', 'Mental Health Support', 'Recovery Resources'],
      highlight: 'Holistic Health Approach'
    },
    {
      category: 'Support & Information',
      icon: HelpCircle,
      title: 'FAQ and Support',
      description: 'Frequently asked questions, emergency information, and comprehensive patient support.',
      link: '/faq',
      features: ['Emergency Information', 'Common Questions', 'Support Resources', 'Preparation Guides'],
      highlight: 'Comprehensive Support'
    }
  ];

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title="Patient Resources"
        subtitle="Comprehensive medical information, assessment tools, and support for your healthcare journey"
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <div className="flex-1">




        {/* Condition Areas */}
        <section className="section-padding-lg bg-gradient-to-br from-background to-primary/5">
          <div className="container-spacing">
            <div className="text-center content-width-standard spacing-lg">
              <h2 className="medical-heading text-primary mb-6">
                Comprehensive Medical Conditions Library
              </h2>
              <p className="medical-lead text-muted-foreground">
                Comprehensive information about neurological and spine conditions, treatment options, and recovery.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              {conditionAreas.map((area) => {
                const IconComponent = area.icon;
                return (
                  <Card
                    key={area.id}
                    className="medical-content-card card-service group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/30"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-center justify-between mb-6">
                        <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-12 w-12 text-primary" />
                        </div>
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-2 bg-success-light/30/10 text-foreground border-success/20">{area.outcomes}</Badge>
                          <div className="text-sm text-muted-foreground font-medium">{area.experience}</div>
                        </div>
                      </div>
                      <CardTitle className="medical-subheading text-primary group-hover:text-primary/80 transition-colors mb-4">
                        {area.title}
                      </CardTitle>
                      <CardDescription className="medical-body mb-6">
                        {area.description}
                      </CardDescription>
                      <Badge className="bg-primary/10 text-primary border-primary/20 mb-4 font-semibold">
                        {area.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        {area.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-success flex-shrink-0" />
                            <span className="text-sm text-muted-foreground font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-between items-center p-4 bg-muted/30 rounded-lg">
                        <span className="text-sm text-muted-foreground font-medium">Conditions covered:</span>
                        <span className="text-sm font-bold text-primary">{area.stats}</span>
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild variant="outline" className="w-full transition-all duration-300 text-wrap">
                        <Link to={area.link} className="flex items-center justify-center gap-2 px-3 py-3 min-h-[52px]">
                          <span className="font-semibold text-center flex-1 leading-tight text-sm md:text-base">
                            Explore {area.title}
                          </span>
                          <ArrowRight className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Additional Resources */}
        <section className="section-padding-lg bg-muted/30">
          <div className="container-spacing">
            <div className="text-center content-width-standard spacing-lg">
              <h2 className="medical-heading text-primary mb-6">
                Additional Support Resources
              </h2>
              <p className="medical-lead text-muted-foreground">
                Comprehensive support resources to assist you throughout your healthcare journey.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              {supportResources.map((resource, index) => {
                const IconComponent = resource.icon;
                return (
                  <Card
                    key={index}
                    className="medical-content-card card-feature group hover:shadow-xl transition-all duration-500 border-2 hover:border-primary/30"
                  >
                    <CardHeader className="pb-6">
                      <Badge variant="outline" className="mb-4 w-fit bg-primary/5 text-primary border-primary/30 font-semibold">{resource.category}</Badge>
                      <div className="p-5 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors w-fit mb-6">
                        <IconComponent className="h-12 w-12 text-primary" />
                      </div>
                      <CardTitle className="medical-subheading text-primary group-hover:text-primary/80 transition-colors mb-4">
                        {resource.title}
                      </CardTitle>
                      <CardDescription className="medical-body mb-6">
                        {resource.description}
                      </CardDescription>
                      <Badge className="bg-success-light/30/10 text-foreground border-success/20 font-semibold">
                        {resource.highlight}
                      </Badge>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="space-y-4">
                        {resource.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-success flex-shrink-0" />
                            <span className="text-sm text-muted-foreground font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild size="lg" variant="default" className="w-full transition-all duration-300 text-wrap">
                        <Link to={resource.link} className="flex items-center justify-center gap-2 px-3 py-3 min-h-[56px]">
                          <span className="font-semibold text-center flex-1 leading-tight text-sm md:text-base">
                            Explore {resource.title}
                          </span>
                          <ArrowRight className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>

            {/* Individual Spine Health Programme Highlight */}
            <Card className="medical-content-card card-featured bg-gradient-to-br from-primary/5 to-background border-2 border-primary/30">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center p-8">
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <SafeImage
                    src="/images/individual-spine-health-1.jpg"
                    alt="Individual Spine Health Programme"
                    className="w-full h-auto"
                    fallbackSrc="/images/patient-resources/spine-health-hero.jpg"
                  />
                </div>
                <div className="space-y-6">
                  <div>
                    <Badge className="mb-6 bg-success-light/30/10 text-foreground border-success/30 font-semibold">
                      Featured Programme
                    </Badge>
                    <h3 className="medical-heading text-primary mb-6">
                      Individual Spine Health Programme
                    </h3>
                    <p className="medical-body mb-6">
                      Our comprehensive programme combines assessment tools, personalised recommendations,
                      and progress tracking to support your spine health journey.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    {[
                      'Comprehensive symptom assessment',
                      'Personalised treatment information',
                      'Progress tracking tools',
                      'Expert guidance integration',
                      'Evidence-based recommendations',
                      'Outcome monitoring'
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-success flex-shrink-0" />
                        <span className="text-sm text-muted-foreground font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className={cn(
                    "flex gap-4",
                    deviceInfo.isMobile ? "flex-col" : "flex-row"
                  )}>
                    <Button asChild size="lg" variant="default" className="text-wrap">
                      <Link to="/patient-resources/individual-spine-health-programme" className="flex items-center gap-2 px-4 py-3 min-h-[56px]">
                        <span className="font-semibold text-center flex-1 leading-tight text-sm md:text-base">
                          Start Programme
                        </span>
                        <ArrowRight className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="lg" className="text-wrap">
                      <Link to="/patient-resources/brain-and-spine-health" className="flex items-center gap-2 px-4 py-3 min-h-[56px]">
                        <span className="font-semibold text-center flex-1 leading-tight text-sm md:text-base">
                          Assessment Tools
                        </span>
                        <ArrowRight className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Assessment Tools */}
        <section className="section-padding-lg bg-muted/30">
          <div className="container-spacing">
            <div className="text-center content-width-standard spacing-lg">
              <h2 className="medical-heading text-primary mb-6">
                Assessment and Decision Support Tools
              </h2>
              <p className="medical-lead text-muted-foreground">
                Evidence-based tools to help you understand your condition and explore treatment options.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {assessmentTools.map((tool) => {
                const IconComponent = tool.icon;
                return (
                  <Card
                    key={tool.id}
                    className="medical-content-card card-tool group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/30"
                  >
                    <CardHeader className="pb-6">
                      <div className="flex items-start justify-between mb-6">
                        <div className="p-4 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <IconComponent className="h-10 w-10 text-primary" />
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className="bg-info-light/30/10 text-foreground border-info/30 font-medium">{tool.timeToComplete}</Badge>
                        </div>
                      </div>
                      <CardTitle className="medical-subheading text-primary group-hover:text-primary/80 transition-colors mb-4">
                        {tool.title}
                      </CardTitle>
                      <CardDescription className="medical-body mb-4">
                        {tool.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="pb-6">
                      <div className="grid grid-cols-2 gap-4">
                        {tool.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-3">
                            <CheckCircle className="h-5 w-5 text-success flex-shrink-0" />
                            <span className="text-sm text-muted-foreground font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>

                    <CardFooter>
                      <Button asChild variant="default" className="w-full transition-all duration-300 text-wrap">
                        <Link to={tool.link} className="flex items-center justify-center gap-2 px-3 py-3 min-h-[56px]">
                          <span className="font-semibold text-center flex-1 leading-tight text-sm md:text-base">
                            Start {tool.title}
                          </span>
                          <ArrowRight className="h-4 w-4 md:h-5 md:w-5 flex-shrink-0" />
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Ready to Take the Next Step?"
          description="Book a consultation with our expert neurosurgical team to discuss your condition and explore treatment options tailored to your needs."
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </div>
    </StandardPageLayout>
  );
};

PatientResources.displayName = 'PatientResources';

export default PatientResources;
