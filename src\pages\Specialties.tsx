import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import React from 'react';
import { Helm<PERSON> } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const specialties = [
  {
    id: 'neurosurgery',
    title: 'Neurosurgery',
    icon: Brain,
    description: 'Comprehensive surgical treatment of brain, spine, and peripheral nerve conditions.',
    areas: [
      'Brain Tumor Surgery',
      'Spine Surgery',
      'Peripheral Nerve Surgery',
      'Vascular Neurosurgery',
      'Functional Neurosurgery'
    ],
    link: '/expertise'
  },
  {
    id: 'spine-surgery',
    title: 'Spine Surgery',
    icon: Activity,
    description: 'Advanced minimally invasive and robotic spine surgery techniques.',
    areas: [
      'Minimally Invasive Surgery',
      'Robotic Spine Surgery',
      'Disc Replacement',
      'Spinal Fusion',
      'Deformity Correction'
    ],
    link: '/expertise/spine-surgery'
  },
  {
    id: 'image-guided',
    title: 'Image-Guided Surgery',
    icon: Zap,
    description: 'Precision surgery using advanced imaging and navigation technologies.',
    areas: [
      'Stereotactic Surgery',
      'Neuronavigation',
      'Intraoperative Imaging',
      'Computer-Assisted Surgery',
      'Real-time Monitoring'
    ],
    link: '/expertise/image-guided-surgery'
  },
  {
    id: 'medicolegal',
    title: 'Medicolegal Services',
    icon: Stethoscope,
    description: 'Independent medical examinations and expert opinions for legal matters.',
    areas: [
      'Independent Medical Examinations',
      'Expert Witness Services',
      'Disability Assessments',
      'Personal Injury Evaluations',
      'Workers Compensation'
    ],
    link: '/medicolegal'
  }
];

const Specialties: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Medical Specialties | Dr. Ales Aliashkevich - Neurosurgeon</title>
        <meta name="description" content="Explore the comprehensive medical specialties offered by Dr. Ales Aliashkevich, including neurosurgery, spine surgery, image-guided procedures, and medicolegal services." />
        <meta name="keywords" content="neurosurgery, spine surgery, image-guided surgery, medicolegal services, brain surgery, Melbourne neurosurgeon" />
      </Helmet>

      <div className="min-h-screen bg-background">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-primary/10 via-background to-muted/30 py-16">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                Medical Specialties
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Dr. Ales Aliashkevich offers comprehensive neurosurgical care across multiple specialties, 
                combining advanced surgical techniques with personalized patient care.
              </p>
            </div>
          </div>
        </div>

        {/* Specialties Grid */}
        <div className="container py-16">
          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {specialties.map((specialty) => {
              const IconComponent = specialty.icon;
              
              return (
                <Card key={specialty.id} className="group hover:shadow-lg transition-all duration-300 border-border/50 hover:border-primary/30">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <CardTitle className="text-xl text-foreground">
                        {specialty.title}
                      </CardTitle>
                    </div>
                    <p className="text-muted-foreground">
                      {specialty.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-3 text-foreground">Key Areas:</h4>
                      <div className="flex flex-wrap gap-2">
                        {specialty.areas.map((area, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="pt-4">
                      <Button asChild variant="outline" className="w-full group">
                        <Link to={specialty.link}>
                          Learn More
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-br from-primary/5 to-muted/30 py-16">
          <div className="container">
            <Card className="max-w-4xl mx-auto text-center">
              <CardContent className="py-12">
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  Expert Neurosurgical Care
                </h2>
                <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                  With extensive experience across multiple neurosurgical specialties, Dr. Aliashkevich 
                  provides comprehensive care tailored to each patient's unique needs.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg">
                    <Link to="/appointments">
                      Schedule Consultation
                    </Link>
                  </Button>
                  
                  <Button variant="outline" size="lg" asChild>
                    <Link to="/contact">
                      Contact Us
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default Specialties;
