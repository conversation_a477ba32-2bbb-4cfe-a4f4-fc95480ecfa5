import { Brain, Disc, Heart, Target } from 'lucide-react';
import React from 'react';
import { Helmet } from 'react-helmet-async';

import PageHeader from '@/components/PageHeader';
import {
  AnatomicalStructuresSection,
  CommonConditionsSection,
  HealthResourcesSection,
  LifestyleStrategiesSection
} from '@/components/patient-resources/brain-spine-health';
import { SectionHeader } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { anatomicalStructures, commonConditions, healthResources, lifestyleCategories } from '@/data/brain-spine-health';


const BrainAndSpineHealth: React.FC = () => {
  const { isMobile } = useDeviceDetection();

  return (
    <StandardPageLayout>
      <Helmet>
        <title>Brain & Spine Health Resources | miNEURO</title>
        <meta name="description" content="Comprehensive resources for brain and spine health, including anatomy guides, condition information, and lifestyle strategies for optimal neurological wellness." />
        <meta name="keywords" content="brain health, spine health, neurological conditions, anatomy, lifestyle strategies, patient resources" />
      </Helmet>

      <PageHeader
        title="Brain & Spine Health Resources"
        subtitle="Comprehensive resources to support your neurological health journey"
        backgroundImage="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=1200&h=400&fit=crop"
      />

      <main className="container mx-auto px-4 py-12">
        <SectionHeader
          title="Explore Health Resources"
          subtitle="Access evidence-based information, tools, and strategies to optimize your brain and spine health"
          className="mb-12"
        />

        <Tabs defaultValue="resources" className="w-full">
          <TabsList className={`grid w-full ${isMobile ? 'grid-cols-2 gap-2' : 'grid-cols-4 gap-1'} mb-8 p-2`}>
            <TabsTrigger value="resources" className="flex items-center justify-center gap-2 font-medium">
              <Brain className="h-4 w-4 flex-shrink-0" />
              <span className={isMobile ? "text-xs" : "text-sm"}>Resources</span>
            </TabsTrigger>
            <TabsTrigger value="anatomy" className="flex items-center justify-center gap-2 font-medium">
              <Target className="h-4 w-4 flex-shrink-0" />
              <span className={isMobile ? "text-xs" : "text-sm"}>Anatomy</span>
            </TabsTrigger>
            <TabsTrigger value="pathophysiology" className="flex items-center justify-center gap-2 font-medium">
              <Disc className="h-4 w-4 flex-shrink-0" />
              <span className={isMobile ? "text-xs" : "text-sm"}>Conditions</span>
            </TabsTrigger>
            <TabsTrigger value="lifestyle" className="flex items-center justify-center gap-2 font-medium">
              <Heart className="h-4 w-4 flex-shrink-0" />
              <span className={isMobile ? "text-xs" : "text-sm"}>Lifestyle</span>
            </TabsTrigger>
          </TabsList>

          <HealthResourcesSection resources={healthResources} />
          <AnatomicalStructuresSection structures={anatomicalStructures} />
          <CommonConditionsSection conditions={commonConditions} />
          <LifestyleStrategiesSection categories={lifestyleCategories} />
        </Tabs>
      </main>
    </StandardPageLayout>
  );
};

export default BrainAndSpineHealth;