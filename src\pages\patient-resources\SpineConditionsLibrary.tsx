import { Search } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';


import PageHeader from '@/components/PageHeader';
import SafeImage from '@/components/SafeImage';
import {
  ConditionCategory,
  IntroductionSection,
  SpineAnatomySection,
  CTASection
} from '@/components/spine-conditions';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/contexts/LanguageContext';
import { spineConditionsPageData } from '@/data/spine-conditions/spineConditionsData';

/**
 * SpineConditionsLibrary Page - Enhanced with Comprehensive Conditions
 * Complete library of spine and neurological conditions with search and filtering
 * Includes recently added conditions: Cervical Myelopathy, Hydrocephalus, Cauda Equina Syndrome
 * Organised by category with urgency indicators and direct navigation
 */
const SpineConditionsLibrary: React.FC = React.memo(() => {
  const { language: _language } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Comprehensive conditions library with new additions
  const comprehensiveConditions = [
    // Spine Conditions - Recently Added
    {
      id: "cervical-myelopathy",
      name: "Cervical Myelopathy",
      description: "Spinal cord compression in the neck causing weakness, numbness, and coordination problems. Early treatment is crucial for optimal outcomes.",
      image: "/images/spine-conditions/cervical-myelopathy-anatomy.jpg",
      category: "spine",
      urgency: "urgent",
      icon: Bone,
      hasDetailedGuide: true
    },
    {
      id: "cauda-equina-syndrome",
      name: "Cauda Equina Syndrome",
      description: "A rare but serious condition where nerve roots at the bottom of the spinal cord are compressed, requiring emergency treatment.",
      image: "/images/spine-conditions/cauda-equina-anatomy.jpg",
      category: "spine",
      urgency: "emergency",
      icon: AlertTriangle,
      hasDetailedGuide: true
    },
    // Brain & Neurological Conditions - Recently Added
    {
      id: "hydrocephalus",
      name: "Hydrocephalus",
      description: "Accumulation of cerebrospinal fluid in the brain ventricles, often requiring shunt placement for drainage.",
      image: "/images/brain-conditions/hydrocephalus-anatomy.jpg",
      category: "brain",
      urgency: "urgent",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "brain-tumour",
      name: "Brain Tumour",
      description: "Abnormal growth of cells in the brain that can be benign or malignant, requiring specialised neurosurgical treatment.",
      image: "/images/brain-conditions/brain-tumour-mri.jpg",
      category: "brain",
      urgency: "urgent",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "chiari-malformation",
      name: "Chiari Malformation",
      description: "A structural defect where brain tissue extends into the spinal canal, potentially requiring surgical decompression.",
      image: "/images/brain-conditions/chiari-malformation-anatomy.jpg",
      category: "brain",
      urgency: "routine",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "trigeminal-neuralgia",
      name: "Trigeminal Neuralgia",
      description: "Severe facial pain caused by trigeminal nerve dysfunction, treatable with medication or surgical procedures.",
      image: "/images/brain-conditions/trigeminal-neuralgia-anatomy.jpg",
      category: "brain",
      urgency: "routine",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "hemifacial-spasm",
      name: "Hemifacial Spasm",
      description: "Involuntary muscle contractions on one side of the face, often treated with botulinum toxin or microvascular decompression.",
      image: "/images/brain-conditions/hemifacial-spasm-anatomy.jpg",
      category: "brain",
      urgency: "routine",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "cerebral-aneurysm",
      name: "Cerebral Aneurysm",
      description: "A weakened area in a brain blood vessel that can rupture, requiring urgent neurosurgical intervention.",
      image: "/images/brain-conditions/cerebral-aneurysm-anatomy.jpg",
      category: "brain",
      urgency: "emergency",
      icon: AlertTriangle,
      hasDetailedGuide: true
    },
    {
      id: "cerebral-avm",
      name: "Cerebral AVM",
      description: "Abnormal connection between arteries and veins in the brain, potentially requiring surgical or endovascular treatment.",
      image: "/images/brain-conditions/cerebral-avm-anatomy.jpg",
      category: "brain",
      urgency: "urgent",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "cerebral-meningioma",
      name: "Cerebral Meningioma",
      description: "A typically benign tumour arising from the meninges, often treatable with surgical resection.",
      image: "/images/brain-conditions/cerebral-meningioma-anatomy.jpg",
      category: "brain",
      urgency: "routine",
      icon: Brain,
      hasDetailedGuide: true
    },
    {
      id: "cerebral-cavernoma",
      name: "Cerebral Cavernoma",
      description: "A cluster of abnormal blood vessels in the brain that may cause seizures or bleeding, sometimes requiring surgical removal.",
      image: "/images/brain-conditions/cerebral-cavernoma-anatomy.jpg",
      category: "brain",
      urgency: "routine",
      icon: Brain,
      hasDetailedGuide: true
    },
    // Traditional Spine Conditions
    {
      id: "herniated-disc",
      name: "Herniated Disc",
      description: "A condition where a disc's soft center pushes through a crack in the tougher exterior casing, causing pain, numbness, or weakness.",
      image: "/images/conditions/cervical-spine-anatomy.jpg",
      category: "spine",
      urgency: "routine",
      icon: Bone,
      hasDetailedGuide: false
    },
    {
      id: "spinal-stenosis",
      name: "Spinal Stenosis",
      description: "Narrowing of the spinal canal that puts pressure on the spinal cord and nerves, causing pain, numbness, and weakness.",
      image: "/images/conditions/spinal-stenosis.jpg",
      category: "spine",
      urgency: "routine",
      icon: Bone,
      hasDetailedGuide: false
    },
    {
      id: "sciatica",
      name: "Sciatica",
      description: "Pain that radiates along the sciatic nerve, which runs from the lower back through the hips and buttocks and down each leg.",
      image: "/images/conditions/sciatica.jpg",
      category: "spine",
      urgency: "routine",
      icon: Activity,
      hasDetailedGuide: false
    }
  ];

  // Filter conditions based on search and category
  const filteredConditions = comprehensiveConditions.filter(condition => {
    const matchesSearch = condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         condition.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || condition.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'badge-emergency';
      case 'urgent': return 'badge-info';
      default: return 'badge-routine';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'Emergency';
      case 'urgent': return 'Urgent';
      default: return 'Routine';
    }
  };

  return (
    <StandardPageLayout title="Spine Conditions Library" showHeader={false}>
      <PageHeader
        title={spineConditionsPageData.pageHeader.title}
        subtitle={spineConditionsPageData.pageHeader.subtitle}
        backgroundImage={spineConditionsPageData.pageHeader.backgroundImage}
        enableParallax={spineConditionsPageData.pageHeader.enableParallax}
      />

      <div className="flex-1">
        {/* Introduction Section */}
        <IntroductionSection
          title={spineConditionsPageData.introduction.title}
          paragraphs={spineConditionsPageData.introduction.paragraphs}
          image={spineConditionsPageData.introduction.image}
        />

        {/* Enhanced Search and Filter Section */}
        <section className="section-background-alt py-20">
          <div className="container">
            <div className="text-center max-w-5xl mx-auto mb-16">
              <h2 className="text-enhanced-heading text-3xl lg:text-4xl font-bold mb-6 text-enhanced-strong">Comprehensive Conditions Library</h2>
              <p className="text-enhanced-muted text-lg leading-relaxed mb-12 max-w-4xl mx-auto">
                Search our comprehensive library of spine and neurological conditions with detailed treatment guides
              </p>

              {/* Search and Filter Controls */}
              <div className="flex flex-col md:flex-row gap-6 max-w-3xl mx-auto mb-12">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
                  <Input
                    type="text"
                    placeholder="Search conditions..."
                    className="pl-12 pr-6 py-4 rounded-xl border-2 border-border medical-card hover:border-primary/40 focus:border-primary/60 transition-all duration-300 text-base font-medium text-card-foreground"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex gap-3">
                  <Button
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('all')}
                    className="rounded-xl px-6 py-4 font-bold border-2 hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    All
                  </Button>
                  <Button
                    variant={selectedCategory === 'spine' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('spine')}
                    className="rounded-xl px-6 py-4 font-bold border-2 hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    Spine
                  </Button>
                  <Button
                    variant={selectedCategory === 'brain' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('brain')}
                    className="rounded-xl px-6 py-4 font-bold border-2 hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    Brain
                  </Button>
                </div>
              </div>

              {/* Results Summary */}
              <div className="text-center">
                <div className="medical-card p-4 inline-block">
                  <p className="text-enhanced-strong">
                    {filteredConditions.length} condition{filteredConditions.length !== 1 ? 's' : ''} found
                    {selectedCategory !== 'all' && ` in ${selectedCategory === 'spine' ? 'Spine' : 'Brain & Neurological'} category`}
                  </p>
                </div>
              </div>
            </div>

            {/* Enhanced Conditions Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
              {filteredConditions.map((condition) => {
                const Icon = condition.icon;

                return (
                  <Card key={condition.id} className="medical-card  transition-all duration-300 hover:scale-105 hover:border-primary/30">
                    <div className="aspect-video w-full overflow-hidden relative rounded-t-xl">
                      <SafeImage
                        src={condition.image}
                        alt={condition.name}
                        className="w-full h-full object-cover"
                        fallbackSrc="/images/conditions/spine-consultation.jpg"
                      />
                      <div className="absolute top-4 right-4 flex gap-3">
                        <Badge className={getUrgencyColor(condition.urgency)}>
                          {getUrgencyLabel(condition.urgency)}
                        </Badge>
                        {condition.hasDetailedGuide && (
                          <Badge className="badge-info">
                            Detailed Guide
                          </Badge>
                        )}
                      </div>
                    </div>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                          <Icon className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-xl font-bold text-enhanced-strong leading-tight">{condition.name}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-6 medical-card-content">
                      <p className="text-enhanced-muted text-base leading-relaxed">
                        {condition.description}
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button asChild className="w-full font-bold py-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                        <Link to={`/patient-resources/conditions/${condition.id}`}>
                          {condition.hasDetailedGuide ? 'View Detailed Guide' : 'Learn More'}
                        </Link>
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>

            {/* No Results Message */}
            {filteredConditions.length === 0 && (
              <div className="text-center py-16">
                <div className="medical-card p-8 max-w-md mx-auto">
                  <p className="text-enhanced text-lg mb-6 font-medium">
                    No conditions found matching your search criteria.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('all');
                    }}
                    className="font-bold px-6 py-3 border-2 hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            )}
          </div>
        </section>



        {/* Condition Categories Tabs */}
        <section className="section-background-alt py-20">
          <div className="container">
            <h2 className="text-enhanced-heading text-3xl lg:text-4xl font-bold mb-12 text-center text-enhanced-strong">{spineConditionsPageData.categoriesTitle}</h2>
            <Tabs defaultValue="cervical" className="w-full max-w-5xl mx-auto">
              <TabsList className="grid grid-cols-3 mb-12 p-2">
                <TabsTrigger value="cervical" className="text-center font-semibold">
                  Cervical Spine
                </TabsTrigger>
                <TabsTrigger value="thoracic" className="text-center font-semibold">
                  Thoracic Spine
                </TabsTrigger>
                <TabsTrigger value="lumbar" className="text-center font-semibold">
                  Lumbar Spine
                </TabsTrigger>
              </TabsList>

              <TabsContent value="cervical">
                <ConditionCategory region={spineConditionsPageData.spineRegions[0]} />
              </TabsContent>

              <TabsContent value="thoracic">
                <ConditionCategory region={spineConditionsPageData.spineRegions[1]} />
              </TabsContent>

              <TabsContent value="lumbar">
                <ConditionCategory region={spineConditionsPageData.spineRegions[2]} />
              </TabsContent>

            </Tabs>
          </div>
        </section>
        {/* Spine Anatomy Section */}
        <SpineAnatomySection
          title={spineConditionsPageData.spineAnatomy.title}
          subtitle={spineConditionsPageData.spineAnatomy.subtitle}
          paragraphs={spineConditionsPageData.spineAnatomy.paragraphs}
          anatomyList={spineConditionsPageData.spineAnatomy.anatomyList}
          image={spineConditionsPageData.spineAnatomy.image}
          ctaButton={spineConditionsPageData.spineAnatomy.ctaButton}
        />

        {/* CTA Section */}
        <CTASection
          title={spineConditionsPageData.cta.title}
          description={spineConditionsPageData.cta.description}
          buttons={spineConditionsPageData.cta.buttons}
        />
      </div>
    </StandardPageLayout>
  );
});

SpineConditionsLibrary.displayName = 'SpineConditionsLibrary';

export default SpineConditionsLibrary;
