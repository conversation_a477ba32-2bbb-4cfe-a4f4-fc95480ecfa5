import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import { 
  CSFAnatomySection, 
  HydrocephalusSymptomAssessment, 
  ShuntSystemsComparison 
} from '@/components/medical-conditions/hydrocephalus';
import {
  ConditionHeroSection,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { hydrocephalusData } from '@/data/conditions/hydrocephalus';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const Hydrocephalus: React.FC = React.memo(() => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Hydrocephalus Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Hydrocephalus: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Comprehensive guide to hydrocephalus: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced shunt systems and comprehensive management." 
        />
        <meta 
          name="keywords" 
          content="hydrocephalus, cerebrospinal fluid, shunt systems, ventriculoperitoneal shunt, brain ventricles, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Hydrocephalus: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Comprehensive guide to hydrocephalus covering causes, symptoms, diagnosis, and treatment options with expert neurosurgical care." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/hydrocephalus" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/hydrocephalus-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Hydrocephalus: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Comprehensive guide to hydrocephalus with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/hydrocephalus" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Hydrocephalus: Comprehensive Patient Guide",
            "description": "Comprehensive guide to hydrocephalus: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/hydrocephalus",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Hydrocephalus",
              "alternateName": ["Water on the Brain", "Cerebrospinal Fluid Accumulation"],
              "description": "Abnormal accumulation of cerebrospinal fluid within the brain's ventricular system",
              "symptom": [
                "Headaches",
                "Nausea and vomiting",
                "Vision problems",
                "Balance and coordination issues",
                "Cognitive changes"
              ],
              "riskFactor": [
                "Neural tube defects",
                "Brain infections",
                "Head trauma",
                "Brain tumours",
                "Premature birth"
              ],
              "medicalSpecialty": "Neurosurgery"
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Hydrocephalus - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHeroSection
            title={hydrocephalusData.hero.title}
            subtitle={hydrocephalusData.hero.subtitle}
            description={hydrocephalusData.overview.description[0]}
            badgeText={hydrocephalusData.hero.badge}
            heroImageSrc={hydrocephalusData.hero.backgroundImage}
            heroImageAlt="Hydrocephalus anatomy"
            showAssessmentButton={true}
            showBookingButton={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={hydrocephalusData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={hydrocephalusData.overview.title}
            description={hydrocephalusData.overview.description}
            keyPoints={hydrocephalusData.overview.keyPoints}
            imageSrc={hydrocephalusData.overview.imageSrc}
            imageAlt={hydrocephalusData.overview.imageAlt}
            imageCaption={hydrocephalusData.overview.imageCaption}
          />

          {/* CSF Anatomy */}
          <CSFAnatomySection
            title={hydrocephalusData.anatomy.title}
            description={hydrocephalusData.anatomy.description}
            csfSystem={hydrocephalusData.anatomy.csfSystem}
          />

          {/* Types and Classifications */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.types.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.types.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {hydrocephalusData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-enhanced-strong font-semibold text-lg">{type.type}</h3>
                      <span className={`text-xs px-2 py-1 rounded font-semibold ${
                        type.type.includes('Communicating') ? 'badge-info' :
                        type.type.includes('Non-communicating') ? 'badge-info' :
                        type.type.includes('Normal Pressure') ? 'badge-routine' :
                        type.type.includes('Congenital') ? 'badge-medical' :
                        'badge-info'
                      }`}>
                        {type.prevalence}
                      </span>
                    </div>
                    <p className="text-enhanced-muted mb-4 text-sm">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="text-enhanced-strong font-medium text-sm mb-2">Key Features:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-enhanced-body text-xs flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Prognosis:</span>
                          <span className="text-enhanced-strong text-xs font-medium">{type.prognosis.split(' ').slice(0, 3).join(' ')}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Treatment:</span>
                          <span className="text-enhanced-strong text-xs font-medium">{type.treatmentApproach.split(' ').slice(0, 3).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Causes and Risk Factors */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.causes.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.causes.description}
                </p>
              </div>

              <div className="content-spacing">
                {hydrocephalusData.causes.etiologies.map((etiology, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-4">{etiology.category}</h3>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {etiology.causes.map((cause, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-enhanced-strong font-medium">{cause.cause}</h4>
                            <span className="badge-info text-xs px-2 py-1 rounded">
                              {cause.frequency}
                            </span>
                          </div>
                          <p className="text-enhanced-muted text-sm mb-2">{cause.description}</p>
                          <p className="text-enhanced-muted text-xs mb-1">
                            <strong className="text-enhanced-strong">Mechanism:</strong> {cause.mechanism}
                          </p>
                          <p className="text-enhanced-muted text-xs">
                            <strong className="text-enhanced-strong">Age Group:</strong> {cause.ageGroup}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Clinical Presentation</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  Hydrocephalus symptoms vary significantly by age group, from infant-specific signs to adult neurological symptoms.
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hydrocephalusData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-center gap-2 text-enhanced-heading font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-4">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded font-semibold ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-info' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-enhanced-muted text-sm mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                          <p className="text-enhanced-muted text-xs">Age Group: {symptom.ageGroup}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <HydrocephalusSymptomAssessment />
          </div>

          {/* Diagnosis Section */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.diagnosis.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.diagnosis.description}
                </p>
              </div>

              {/* Clinical Assessment */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-enhanced-heading text-2xl font-bold mb-6 text-center">Clinical Assessment</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {hydrocephalusData.diagnosis.clinicalAssessment.map((assessment, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="text-enhanced-heading font-semibold text-lg mb-3">{assessment.assessment}</h4>
                      <p className="text-enhanced-muted mb-4 text-sm">{assessment.description}</p>

                      <div className="space-y-3">
                        <div>
                          <h5 className="text-enhanced-strong font-medium text-sm mb-2">Key Findings:</h5>
                          <ul className="space-y-1">
                            {assessment.findings.map((finding, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                                {finding}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-info-light border border-info/30 rounded p-3">
                          <h5 className="text-enhanced-strong font-medium text-sm text-info mb-1">Clinical Significance</h5>
                          <p className="text-enhanced-body text-sm text-info">{assessment.significance}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Investigations */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-enhanced-heading text-2xl font-bold mb-6 text-center">Diagnostic Investigations</h3>
                <div className="grid gap-8 lg:grid-cols-3">
                  {hydrocephalusData.diagnosis.investigations.map((investigation, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="text-enhanced-heading font-semibold text-xl mb-3">{investigation.test}</h4>
                      <p className="text-enhanced-muted mb-4">{investigation.description}</p>

                      <div className="space-y-3">
                        <div className="bg-success-light border border-success/30 rounded p-3">
                          <h5 className="text-enhanced-strong font-medium text-sm text-success mb-1">Accuracy</h5>
                          <p className="text-enhanced-body text-sm text-success">{investigation.accuracy}</p>
                        </div>

                        <div>
                          <h5 className="text-enhanced-strong font-medium text-sm mb-2">Advantages:</h5>
                          <ul className="space-y-1">
                            {investigation.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-enhanced-strong font-medium text-sm mb-2">Limitations:</h5>
                          <ul className="space-y-1">
                            {investigation.limitations.slice(0, 2).map((limitation, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                                {limitation}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Differential Diagnosis */}
              <div>
                <h3 className="text-enhanced-heading text-enhanced-heading text-2xl font-bold mb-6 text-center">Differential Diagnosis</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {hydrocephalusData.diagnosis.differentialDiagnosis.map((condition, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="text-enhanced-heading font-semibold text-lg mb-3">{condition.condition}</h4>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-enhanced-strong font-medium text-sm mb-2">Distinguishing Features:</h5>
                          <ul className="space-y-1">
                            {condition.distinguishingFeatures.map((feature, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-enhanced-accent rounded-full mt-1.5 flex-shrink-0" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-info-light border border-info/30 rounded p-3">
                          <h5 className="text-enhanced-strong font-medium text-sm text-info mb-1">Key Differences</h5>
                          <p className="text-enhanced-body text-sm text-info">{condition.keyDifferences}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Treatment Modalities */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.treatmentModalities.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.treatmentModalities.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {hydrocephalusData.treatmentModalities.treatments.map((treatment, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-3">{treatment.name}</h3>
                    <p className="text-enhanced-muted mb-4">{treatment.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="text-enhanced-strong font-medium text-sm text-success mb-1">Success Rate</h4>
                        <p className="text-enhanced-body text-sm text-success">{treatment.successRate}</p>
                      </div>

                      <div className="content-spacing-sm">
                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Indications:</h4>
                          <ul className="space-y-1">
                            {treatment.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                                {indication}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Advantages:</h4>
                          <ul className="space-y-1">
                            {treatment.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Disadvantages:</h4>
                          <ul className="space-y-1">
                            {treatment.disadvantages.slice(0, 3).map((disadvantage, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                                {disadvantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Duration:</span>
                        <span className="text-enhanced-strong text-xs font-medium">{treatment.duration}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Surgical Options */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.surgicalOptions.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {hydrocephalusData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-heading font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-enhanced-muted mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="text-enhanced-strong font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-enhanced-body text-sm text-info">{procedure.technique}</p>
                      </div>

                      <div className="grid gap-4 md:grid-cols-1">
                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Indications:</h4>
                          <ul className="space-y-1">
                            {procedure.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-enhanced-accent rounded-full mt-1.5 flex-shrink-0" />
                                {indication}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Advantages:</h4>
                          <ul className="space-y-1">
                            {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-strong font-medium text-sm mb-2">Risks:</h4>
                          <ul className="space-y-1">
                            {procedure.risks.slice(0, 3).map((risk, idx) => (
                              <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-emergency rounded-full mt-1.5 flex-shrink-0" />
                                {risk}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex justify-between items-center pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-enhanced-strong text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Shunt Systems Comparison */}
          <ShuntSystemsComparison
            title={hydrocephalusData.shuntSystems.title}
            description={hydrocephalusData.shuntSystems.description}
            types={hydrocephalusData.shuntSystems.types}
          />

          {/* Prognosis and Outcomes */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.prognosis.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.prognosis.description}
                </p>
              </div>

              {/* Prognostic Factors */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-enhanced-heading text-2xl font-bold mb-6 text-center">Factors Affecting Prognosis</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {hydrocephalusData.prognosis.factors.map((factor, index) => (
                    <div key={index} className="medical-card p-6 text-center">
                      <h4 className="text-enhanced-strong font-semibold text-lg mb-2">{factor.factor}</h4>
                      <div className="mb-3">
                        <span className={`text-xs px-3 py-1.5 rounded-full font-medium ${
                          factor.impact === 'Critical prognostic factor' ? 'badge-emergency' :
                          factor.impact === 'Major influence on prognosis' ? 'badge-info' :
                          factor.impact === 'Significant factor' ? 'badge-info' :
                          'badge-info'
                        }`}>
                          {factor.impact}
                        </span>
                      </div>
                      <p className="text-enhanced-muted text-sm">{factor.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Outcome Scenarios */}
              <div>
                <h3 className="text-enhanced-heading text-enhanced-heading text-2xl font-bold mb-6 text-center">Expected Outcomes</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {hydrocephalusData.prognosis.outcomes.map((outcome, index) => (
                    <div key={index} className="medical-card p-6">
                      <h4 className="text-enhanced-strong font-semibold text-xl mb-3">{outcome.scenario}</h4>
                      <div className="mb-4">
                        <span className={`text-sm px-3 py-1.5 rounded-full font-medium ${
                          outcome.scenario.includes('Early') ? 'badge-routine' :
                          outcome.scenario.includes('Delayed') ? 'badge-info' :
                          'badge-info'
                        }`}>
                          {outcome.expectedOutcome}
                        </span>
                      </div>
                      <div className="space-y-3">
                        <h5 className="text-enhanced-strong font-medium text-sm">Key Factors:</h5>
                        <ul className="space-y-2">
                          {outcome.factors.map((factor, idx) => (
                            <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                              <div className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {factor}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Living with Hydrocephalus */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.livingWithHydrocephalus.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.livingWithHydrocephalus.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hydrocephalusData.livingWithHydrocephalus.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-strong font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-body text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-3">
                      <h4 className="text-enhanced-strong font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-2">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-enhanced-body text-xs flex items-start gap-2">
                            <div className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.followUpCare.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hydrocephalusData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-strong font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-enhanced-body text-sm mb-4">{timeframe.purpose}</p>
                    <div className="space-y-3">
                      <h4 className="text-enhanced-strong font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-2">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-enhanced-body text-sm flex items-start gap-2">
                            <div className="w-2 h-2 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-16 section-background">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.supportResources.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hydrocephalusData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="text-enhanced-strong font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border/30 pb-3 last:border-b-0">
                          <h4 className="text-enhanced-strong font-medium mb-1">{item.name}</h4>
                          <p className="text-enhanced-body text-sm mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-primary text-xs font-medium">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs */}
          <section className="py-16 section-background-emergency">
            <div className="container">
              <div className="text-center mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{hydrocephalusData.warningSigns.title}</h2>
                <p className="text-enhanced-body text-lg max-w-3xl mx-auto">
                  {hydrocephalusData.warningSigns.description}
                </p>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {hydrocephalusData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="medical-card p-6 border-emergency/30">
                    <h3 className="text-enhanced-heading font-semibold text-lg mb-2">{sign.sign}</h3>
                    <p className="text-enhanced-body text-sm mb-3">{sign.description}</p>
                    <div className="bg-emergency-light border border-emergency/30 rounded p-3 mb-3">
                      <p className="text-enhanced-strong text-sm font-medium text-emergency">{sign.action}</p>
                    </div>
                    <div>
                      <span className={`text-xs px-3 py-1.5 rounded-full font-bold ${
                        sign.urgency === 'immediate' ? 'badge-emergency' :
                        sign.urgency === 'urgent' ? 'badge-info' :
                        'badge-info'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
});

export default Hydrocephalus;
