/**
 * Core application routes module
 * Contains essential pages like home, appointments, contact, etc.
 */

import { ROUTE_PATHS } from '../route-definitions';

import { RouteModule } from './types';

export const coreRoutesModule: RouteModule = {
  name: 'core',
  category: 'core',
  priority: 'high',
  preload: [
    ROUTE_PATHS.HOME,
    ROUTE_PATHS.APPOINTMENTS,
    ROUTE_PATHS.CONTACT
  ],
  routes: {
    // Essential pages
    [ROUTE_PATHS.HOME]: () => import('@/pages/Index'),
    [ROUTE_PATHS.APPOINTMENTS]: () => import('@/pages/Appointments'),
    [ROUTE_PATHS.CONTACT]: () => import('@/pages/Contact'),
    [ROUTE_PATHS.FAQ]: () => import('@/pages/Faq'),
    
    // Legal and policy pages
    [ROUTE_PATHS.PRIVACY_POLICY]: () => import('@/pages/PrivacyPolicy'),
    [ROUTE_PATHS.TERMS_CONDITIONS]: () => import('@/pages/TermsConditions'),
    
    // General information pages
    [ROUTE_PATHS.GALLERY]: () => import('@/pages/Gallery'),
    [ROUTE_PATHS.CONSULTING_ROOMS]: () => import('@/pages/ConsultingRooms'),
    [ROUTE_PATHS.MEDICOLEGAL]: () => import('@/pages/Medicolegal'),
    [ROUTE_PATHS.SPECIALTIES]: () => import('@/pages/Specialties'),
    
    // Special routes
    [ROUTE_PATHS.NOT_FOUND]: () => import('@/pages/NotFound'),
  }
};

// Export route paths for this module
export const coreRoutePaths = Object.keys(coreRoutesModule.routes);

// Export metadata for core routes
export const coreRouteMetadata = {
  [ROUTE_PATHS.HOME]: {
    title: 'miNEURO - Expert Neurosurgical Care',
    description: 'Leading neurosurgical practice providing expert spine and brain care across Melbourne',
    keywords: ['neurosurgery', 'spine surgery', 'brain surgery', 'Melbourne'],
    category: 'core' as const,
    priority: 'high' as const,
    changeFreq: 'weekly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.APPOINTMENTS]: {
    title: 'Book Appointment',
    description: 'Schedule your neurosurgical consultation with Dr. Ales Aliashkevich',
    keywords: ['appointments', 'booking', 'consultation', 'neurosurgery'],
    category: 'core' as const,
    priority: 'high' as const,
    changeFreq: 'daily' as const,
    module: 'core'
  },
  [ROUTE_PATHS.CONTACT]: {
    title: 'Contact Us',
    description: 'Get in touch with our neurosurgical team for appointments and consultations',
    keywords: ['contact', 'appointments', 'consultation'],
    category: 'core' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.FAQ]: {
    title: 'Frequently Asked Questions',
    description: 'Common questions about neurosurgery, spine surgery, and treatments',
    keywords: ['FAQ', 'questions', 'neurosurgery', 'spine surgery'],
    category: 'core' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.GALLERY]: {
    title: 'Medical Facility Gallery',
    description: 'Explore our state-of-the-art medical facilities and advanced neurosurgical equipment',
    keywords: ['gallery', 'facilities', 'equipment', 'neurosurgery'],
    category: 'core' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.CONSULTING_ROOMS]: {
    title: 'Consulting Room Rental',
    description: 'Rent fully-equipped medical consulting rooms in Surrey Hills and surrounding areas',
    keywords: ['consulting rooms', 'medical rental', 'Surrey Hills', 'healthcare'],
    category: 'core' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.MEDICOLEGAL]: {
    title: 'Medico-Legal Services',
    description: 'Expert medico-legal assessments and reports',
    keywords: ['medico-legal', 'assessments', 'reports', 'expert'],
    category: 'core' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.SPECIALTIES]: {
    title: 'Neurosurgical Specialties',
    description: 'Specialised neurosurgical procedures and treatments',
    keywords: ['specialties', 'procedures', 'neurosurgery', 'treatments'],
    category: 'core' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.PRIVACY_POLICY]: {
    title: 'Privacy Policy',
    description: 'Privacy policy and data protection information',
    keywords: ['privacy', 'data protection', 'policy'],
    category: 'core' as const,
    priority: 'low' as const,
    changeFreq: 'yearly' as const,
    module: 'core'
  },
  [ROUTE_PATHS.TERMS_CONDITIONS]: {
    title: 'Terms & Conditions',
    description: 'Terms of service and usage conditions',
    keywords: ['terms', 'conditions', 'service'],
    category: 'core' as const,
    priority: 'low' as const,
    changeFreq: 'yearly' as const,
    module: 'core'
  }
};
