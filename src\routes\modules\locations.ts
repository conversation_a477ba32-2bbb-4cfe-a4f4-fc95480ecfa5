/**
 * Locations routes module
 * Contains all clinic location pages
 */

import { ROUTE_PATHS } from '../route-definitions';

import { RouteModule } from './types';

export const locationsModule: RouteModule = {
  name: 'locations',
  category: 'locations',
  priority: 'medium',
  preload: [
    ROUTE_PATHS.LOCATIONS,
    ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS,
    ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON
  ],
  routes: {
    // Main locations page
    [ROUTE_PATHS.LOCATIONS]: () => import('@/pages/Locations'),

    // Individual location pages
    [ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS]: () => import('@/pages/locations/surrey-hills'),
    [ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON]: () => import('@/pages/locations/mornington'),
    [ROUTE_PATHS.LOCATION_ROUTES.FRANKSTON]: () => import('@/pages/locations/frankston'),
    [ROUTE_PATHS.LOCATION_ROUTES.LANGWARRIN]: () => import('@/pages/locations/langwarrin'),
    [ROUTE_PATHS.LOCATION_ROUTES.BUNDOORA]: () => import('@/pages/locations/bundoora'),
    [ROUTE_PATHS.LOCATION_ROUTES.WERRIBEE]: () => import('@/pages/locations/werribee'),
    [ROUTE_PATHS.LOCATION_ROUTES.HEIDELBERG]: () => import('@/pages/locations/heidelberg'),
    [ROUTE_PATHS.LOCATION_ROUTES.MOONEE_PONDS]: () => import('@/pages/locations/moonee-ponds'),
    [ROUTE_PATHS.LOCATION_ROUTES.SUNBURY]: () => import('@/pages/locations/sunbury'),
    [ROUTE_PATHS.LOCATION_ROUTES.DANDENONG]: () => import('@/pages/locations/dandenong'),
    [ROUTE_PATHS.LOCATION_ROUTES.WANTIRNA]: () => import('@/pages/locations/wantirna'),
    [ROUTE_PATHS.LOCATION_ROUTES.LOCATION_DETAIL]: () => import('@/pages/locations/LocationDetail'),
  }
};

// Export route paths for this module
export const locationsRoutePaths = Object.keys(locationsModule.routes);

// Export metadata for location routes
export const locationsRouteMetadata = {
  [ROUTE_PATHS.LOCATIONS]: {
    title: 'Our Locations',
    description: 'Find our neurosurgical clinics across Melbourne and surrounding areas',
    keywords: ['locations', 'clinics', 'Melbourne', 'neurosurgery'],
    category: 'locations' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'locations'
  },
  [ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS]: {
    title: 'Surrey Hills Clinic',
    description: 'Neurosurgical services at our Surrey Hills clinic location',
    keywords: ['Surrey Hills', 'clinic', 'neurosurgery', 'Melbourne'],
    category: 'locations' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'locations'
  },
  [ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON]: {
    title: 'Mornington Clinic',
    description: 'Neurosurgical services at our Mornington clinic location',
    keywords: ['Mornington', 'clinic', 'neurosurgery', 'Peninsula'],
    category: 'locations' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'locations'
  },
  [ROUTE_PATHS.LOCATION_ROUTES.FRANKSTON]: {
    title: 'Frankston Clinic',
    description: 'Neurosurgical services at our Frankston clinic location',
    keywords: ['Frankston', 'clinic', 'neurosurgery', 'Peninsula'],
    category: 'locations' as const,
    priority: 'medium' as const,
    changeFreq: 'monthly' as const,
    module: 'locations'
  }
};
