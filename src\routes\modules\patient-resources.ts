/**
 * Patient Resources routes module
 * Contains all patient education and resource pages
 */

import { ROUTE_PATHS } from '../route-definitions';

import { RouteModule } from './types';

export const patientResourcesModule: RouteModule = {
  name: 'patient-resources',
  category: 'patient-resources',
  priority: 'high',
  preload: [
    ROUTE_PATHS.PATIENT_RESOURCES,
    ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS,
    ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_CONDITIONS
  ],
  routes: {
    // Main patient resources page
    [ROUTE_PATHS.PATIENT_RESOURCES]: () => import('@/pages/PatientResources'),

    // Category landing pages
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_CONDITIONS]: () => import('@/pages/patient-resources/BrainConditions'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS]: () => import('@/pages/patient-resources/SpineConditions'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PERIPHERAL_NERVE_CONDITIONS]: () => import('@/pages/patient-resources/PeripheralNerveConditions'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_LIBRARY]: () => import('@/pages/patient-resources/ExerciseLibrary'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_AND_SPINE_HEALTH]: () => import('@/pages/patient-resources/BrainAndSpineHealth'),

    // Educational content pages
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_ANATOMY]: () => import('@/pages/patient-resources/SpineAnatomy'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_ANATOMY]: () => import('@/pages/patient-resources/BrainAnatomy'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.NERVE_ANATOMY]: () => import('@/pages/patient-resources/NerveAnatomy'),

    // Assessment and tools
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.ASSESSMENT_TOOLS]: () => import('@/pages/patient-resources/AssessmentTools'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PATIENT_DASHBOARD]: () => import('@/pages/patient-resources/PatientDashboard'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_HEALTH_APP]: () => import('@/pages/patient-resources/SpineHealthApp'),

    // Exercise and lifestyle pages
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_EXERCISES]: () => import('@/pages/patient-resources/CervicalSpineExercises'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.LUMBAR_SPINE_EXERCISES]: () => import('@/pages/patient-resources/LumbarSpineExercises'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_SAFE_EXERCISES]: () => import('@/pages/patient-resources/SpineSafeExercises'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_SAFE_EXERCISES_DEGENERATIVE_DISC]: () => import('@/pages/patient-resources/SpineSafeExercisesDegenerativeDisc'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.LIFESTYLE_MODIFICATIONS]: () => import('@/pages/patient-resources/LifestyleModifications'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.AGE_SPECIFIC_SPINE_RECOMMENDATIONS]: () => import('@/pages/patient-resources/AgeSpecificSpineRecommendations'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.YOUTHFUL_SPINE]: () => import('@/pages/patient-resources/YouthfulSpine'),

    // Health programs and guides
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAMME]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXPERT_GUIDE_SPINE_BRAIN_HEALTH]: () => import('@/pages/patient-resources/ExpertGuideSpineBrainHealth'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_AND_BRAIN_HEALTH]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),

    // Injury and condition information
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_INJURY]: () => import('@/pages/patient-resources/CervicalSpineInjury'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFORMATION]: () => import('@/pages/patient-resources/ConditionInformation'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS_LIBRARY]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PERIPHERAL_NERVE_CONDITIONS_LIBRARY]: () => import('@/pages/patient-resources/PeripheralNerveConditionsLibrary'),

    // Exercise and medication guidance
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_PAIN_MED_RISKS]: () => import('@/pages/patient-resources/ExercisePainMedRisks'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISES_PAIN_MEDICATION_TRANSITION]: () => import('@/pages/patient-resources/ExercisesPainMedicationTransition'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.HIDDEN_RISKS_EXERCISING_PAIN_MEDICATIONS]: () => import('@/pages/patient-resources/HiddenRisksExercisingPainMedications'),

    // Legacy compatibility routes
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFO]: () => import('@/pages/patient-resources/ConditionInformation'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SURGICAL_TECHNOLOGIES]: () => import('@/pages/patient-resources/SpineHealthApp'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.RECOVERY_REHABILITATION]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),
    [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAM]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),
  }
};

// Export route paths for this module
export const patientResourcesRoutePaths = Object.keys(patientResourcesModule.routes);

// Export metadata for patient resources routes (partial - will be extended)
export const patientResourcesRouteMetadata = {
  [ROUTE_PATHS.PATIENT_RESOURCES]: {
    title: 'Patient Resources',
    description: 'Comprehensive resources for spine health, exercises, and condition information',
    keywords: ['patient resources', 'spine health', 'exercises', 'conditions'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'weekly' as const,
    module: 'patient-resources'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_ANATOMY]: {
    title: 'Spine Anatomy Guide',
    description: 'Comprehensive guide to spine anatomy, structure, and function for patient education',
    keywords: ['spine anatomy', 'vertebrae', 'spinal cord', 'anatomy education', 'spine structure'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'patient-resources'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS_LIBRARY]: {
    title: 'Spine & Neurological Conditions Library',
    description: 'Comprehensive library of spine and neurological conditions with detailed information and treatment options',
    keywords: ['spine conditions', 'neurological conditions', 'spinal disorders', 'brain conditions', 'back pain', 'neck pain', 'spine health'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'patient-resources'
  },
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.ASSESSMENT_TOOLS]: {
    title: 'Assessment Tools',
    description: 'Interactive assessment tools for spine and neurological conditions to help evaluate symptoms and guide treatment decisions',
    keywords: ['symptom assessment', 'spine assessment', 'neurological assessment', 'health tools', 'condition evaluation'],
    category: 'patient-resources' as const,
    priority: 'high' as const,
    changeFreq: 'monthly' as const,
    module: 'patient-resources'
  }
};
