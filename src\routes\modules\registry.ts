/**
 * Centralized route registry system
 * Combines all modular route configurations
 */

import { ROUTE_REDIRECTS } from '../route-definitions';

import { conditionsModule, conditionsRouteMetadata } from './conditions';
import { coreRoutesModule, coreRouteMetadata } from './core';
import { expertiseModule, expertiseRouteMetadata } from './expertise';
import { gpResourcesModule, gpResourcesRouteMetadata } from './gp-resources';
import { locationsModule, locationsRouteMetadata } from './locations';
import { patientResourcesModule, patientResourcesRouteMetadata } from './patient-resources';
import { RouteModule, RouteRegistry, RouteValidationResult, RouteMetadata } from './types';


/**
 * Modular Route Registry
 * Manages all route modules and provides centralized access
 */
export class ModularRouteRegistry {
  private static instance: ModularRouteRegistry;
  private registry: RouteRegistry;
  private modules: RouteModule[];

  private constructor() {
    this.registry = {
      modules: new Map(),
      routes: new Map(),
      preloadedRoutes: new Set()
    };
    
    this.modules = [
      coreRoutesModule,
      patientResourcesModule,
      conditionsModule,
      expertiseModule,
      locationsModule,
      gpResourcesModule
    ];

    this.initializeRegistry();
  }

  static getInstance(): ModularRouteRegistry {
    if (!ModularRouteRegistry.instance) {
      ModularRouteRegistry.instance = new ModularRouteRegistry();
    }
    return ModularRouteRegistry.instance;
  }

  /**
   * Initialize the registry with all modules
   */
  private initializeRegistry(): void {
    this.modules.forEach(module => {
      // Register the module
      this.registry.modules.set(module.name, module);

      // Register all routes from the module
      Object.entries(module.routes).forEach(([path, loader]) => {
        if (this.registry.routes.has(path)) {
          if (import.meta.env.DEV) {
            console.warn(`Route ${path} is already registered. Overwriting with module ${module.name}`);
          }
        }
        this.registry.routes.set(path, loader);
      });
    });
  }

  /**
   * Get all registered routes
   */
  getAllRoutes(): string[] {
    return Array.from(this.registry.routes.keys());
  }

  /**
   * Get routes by category
   */
  getRoutesByCategory(category: RouteModule['category']): string[] {
    const routes: string[] = [];
    
    this.registry.modules.forEach(module => {
      if (module.category === category) {
        routes.push(...Object.keys(module.routes));
      }
    });
    
    return routes;
  }

  /**
   * Get routes by module name
   */
  getRoutesByModule(moduleName: string): string[] {
    const module = this.registry.modules.get(moduleName);
    return module ? Object.keys(module.routes) : [];
  }

  /**
   * Get route loader for a specific path
   */
  getRouteLoader(path: string) {
    return this.registry.routes.get(path);
  }

  /**
   * Check if a route exists
   */
  hasRoute(path: string): boolean {
    return this.registry.routes.has(path);
  }

  /**
   * Get all modules
   */
  getModules(): RouteModule[] {
    return this.modules;
  }

  /**
   * Get module by name
   */
  getModule(name: string): RouteModule | undefined {
    return this.registry.modules.get(name);
  }

  /**
   * Get high priority routes for preloading
   */
  getHighPriorityRoutes(): string[] {
    const highPriorityRoutes: string[] = [];
    
    this.registry.modules.forEach(module => {
      if (module.priority === 'high' && module.preload) {
        highPriorityRoutes.push(...module.preload);
      }
    });
    
    return [...new Set(highPriorityRoutes)]; // Remove duplicates
  }

  /**
   * Get routes to preload for a specific module
   */
  getPreloadRoutesForModule(moduleName: string): string[] {
    const module = this.registry.modules.get(moduleName);
    return module?.preload || [];
  }

  /**
   * Validate all routes
   */
  validateRoutes(): RouteValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingRoutes: string[] = [];
    const duplicateRoutes: string[] = [];
    
    // Check for duplicate routes across modules
    const routeCounts = new Map<string, number>();
    
    this.registry.modules.forEach(module => {
      Object.keys(module.routes).forEach(path => {
        const count = routeCounts.get(path) || 0;
        routeCounts.set(path, count + 1);
        
        if (count > 0) {
          duplicateRoutes.push(path);
        }
      });
    });

    // Check for missing preload routes
    this.registry.modules.forEach(module => {
      if (module.preload) {
        module.preload.forEach(preloadPath => {
          if (!this.registry.routes.has(preloadPath)) {
            missingRoutes.push(preloadPath);
            errors.push(`Preload route ${preloadPath} in module ${module.name} is not registered`);
          }
        });
      }
    });

    // Check module consistency
    this.registry.modules.forEach(module => {
      if (Object.keys(module.routes).length === 0) {
        warnings.push(`Module ${module.name} has no routes defined`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      missingRoutes,
      duplicateRoutes
    };
  }

  /**
   * Get combined route metadata from all modules
   */
  getAllRouteMetadata(): Record<string, RouteMetadata> {
    return {
      ...coreRouteMetadata,
      ...patientResourcesRouteMetadata,
      ...conditionsRouteMetadata,
      ...expertiseRouteMetadata,
      ...locationsRouteMetadata,
      ...gpResourcesRouteMetadata
    };
  }

  /**
   * Get route redirects
   */
  getRouteRedirects(): Record<string, string> {
    return ROUTE_REDIRECTS;
  }

  /**
   * Get statistics about the route registry
   */
  getStatistics() {
    const stats = {
      totalModules: this.registry.modules.size,
      totalRoutes: this.registry.routes.size,
      routesByCategory: {} as Record<string, number>,
      routesByPriority: {} as Record<string, number>,
      preloadableRoutes: 0
    };

    this.registry.modules.forEach(module => {
      // Count by category
      stats.routesByCategory[module.category] = 
        (stats.routesByCategory[module.category] || 0) + Object.keys(module.routes).length;
      
      // Count by priority
      stats.routesByPriority[module.priority] = 
        (stats.routesByPriority[module.priority] || 0) + Object.keys(module.routes).length;
      
      // Count preloadable routes
      if (module.preload) {
        stats.preloadableRoutes += module.preload.length;
      }
    });

    return stats;
  }
}

// Export singleton instance
export const routeRegistry = ModularRouteRegistry.getInstance();

// Export convenience functions
export const getAllRoutes = () => routeRegistry.getAllRoutes();
export const getRoutesByCategory = (category: RouteModule['category']) => 
  routeRegistry.getRoutesByCategory(category);
export const getRouteLoader = (path: string) => routeRegistry.getRouteLoader(path);
export const hasRoute = (path: string) => routeRegistry.hasRoute(path);
export const validateRoutes = () => routeRegistry.validateRoutes();
export const getAllRouteMetadata = () => routeRegistry.getAllRouteMetadata();
export const getHighPriorityRoutes = () => routeRegistry.getHighPriorityRoutes();
export const getRouteRedirects = () => routeRegistry.getRouteRedirects();
