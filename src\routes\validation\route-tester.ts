/**
 * Route testing utilities for automated validation
 */

import { routeRegistry } from '../modules';


export interface RouteTestResult {
  path: string;
  module: string;
  passed: boolean;
  loadTime: number;
  error?: string;
  warnings: string[];
}

export interface RouteTestSuite {
  timestamp: string;
  totalTests: number;
  passed: number;
  failed: number;
  averageLoadTime: number;
  results: RouteTestResult[];
  summary: {
    byModule: Record<string, { passed: number; failed: number; avgLoadTime: number }>;
    slowestRoutes: Array<{ path: string; loadTime: number }>;
    failedRoutes: Array<{ path: string; error: string }>;
  };
}

/**
 * Route Testing System
 */
export class RouteTester {
  private static instance: RouteTester;

  static getInstance(): RouteTester {
    if (!RouteTester.instance) {
      RouteTester.instance = new RouteTester();
    }
    return RouteTester.instance;
  }

  /**
   * Test all routes
   */
  async testAllRoutes(): Promise<RouteTestSuite> {
    const startTime = Date.now();
    const allRoutes = routeRegistry.getAllRoutes();
    const modules = routeRegistry.getModules();
    
    console.log(`🧪 Testing ${allRoutes.length} routes...`);

    const results: RouteTestResult[] = [];
    
    // Test each route
    for (const path of allRoutes) {
      const result = await this.testRoute(path);
      results.push(result);
      
      // Log progress
      if (results.length % 10 === 0) {
        console.log(`📊 Tested ${results.length}/${allRoutes.length} routes`);
      }
    }

    // Generate summary
    const summary = this.generateSummary(results, modules);
    
    const testSuite: RouteTestSuite = {
      timestamp: new Date().toISOString(),
      totalTests: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
      averageLoadTime: results.reduce((sum, r) => sum + r.loadTime, 0) / results.length,
      results,
      summary
    };

    const totalTime = Date.now() - startTime;
    console.log(`✅ Route testing completed in ${totalTime}ms`);
    console.log(`📊 Results: ${testSuite.passed}/${testSuite.totalTests} passed`);

    return testSuite;
  }

  /**
   * Test a specific route
   */
  async testRoute(path: string): Promise<RouteTestResult> {
    const startTime = performance.now();
    const module = this.getModuleForRoute(path);
    const warnings: string[] = [];
    
    try {
      const loader = routeRegistry.getRouteLoader(path);
      
      if (!loader) {
        return {
          path,
          module,
          passed: false,
          loadTime: 0,
          error: 'No loader function found',
          warnings
        };
      }

      // Test component loading
      const component = await loader();
      const loadTime = performance.now() - startTime;

      // Validate component
      if (!component || !component.default) {
        warnings.push('Component does not have default export');
      }

      // Check for common issues
      if (loadTime > 1000) {
        warnings.push(`Slow loading time: ${loadTime.toFixed(2)}ms`);
      }

      return {
        path,
        module,
        passed: true,
        loadTime,
        warnings
      };

    } catch (error) {
      const loadTime = performance.now() - startTime;
      
      return {
        path,
        module,
        passed: false,
        loadTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        warnings
      };
    }
  }

  /**
   * Test routes by module
   */
  async testModule(moduleName: string): Promise<RouteTestResult[]> {
    const module = routeRegistry.getModule(moduleName);
    
    if (!module) {
      throw new Error(`Module ${moduleName} not found`);
    }

    const routePaths = Object.keys(module.routes);
    const results: RouteTestResult[] = [];

    console.log(`🧪 Testing ${routePaths.length} routes in module: ${moduleName}`);

    for (const path of routePaths) {
      const result = await this.testRoute(path);
      results.push(result);
    }

    return results;
  }

  /**
   * Test high-priority routes
   */
  async testHighPriorityRoutes(): Promise<RouteTestResult[]> {
    const modules = routeRegistry.getModules();
    const highPriorityPaths: string[] = [];

    modules.forEach(module => {
      if (module.priority === 'high' && module.preload) {
        highPriorityPaths.push(...module.preload);
      }
    });

    console.log(`🧪 Testing ${highPriorityPaths.length} high-priority routes`);

    const results: RouteTestResult[] = [];
    for (const path of highPriorityPaths) {
      const result = await this.testRoute(path);
      results.push(result);
    }

    return results;
  }

  /**
   * Performance benchmark for routes
   */
  async benchmarkRoutes(iterations: number = 3): Promise<Record<string, number>> {
    const allRoutes = routeRegistry.getAllRoutes();
    const benchmarks: Record<string, number> = {};

    console.log(`⚡ Benchmarking ${allRoutes.length} routes with ${iterations} iterations each`);

    for (const path of allRoutes) {
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        try {
          const loader = routeRegistry.getRouteLoader(path);
          if (loader) {
            await loader();
          }
        } catch {
          // Ignore errors for benchmarking
        }
        
        times.push(performance.now() - startTime);
      }

      benchmarks[path] = times.reduce((sum, time) => sum + time, 0) / times.length;
    }

    return benchmarks;
  }

  /**
   * Generate test summary
   */
  private generateSummary(results: RouteTestResult[], modules: Array<{ name: string }>) {
    const byModule: Record<string, { passed: number; failed: number; avgLoadTime: number }> = {};
    
    // Initialize module stats
    modules.forEach(module => {
      byModule[module.name] = { passed: 0, failed: 0, avgLoadTime: 0 };
    });

    // Calculate module statistics
    results.forEach(result => {
      const moduleStats = byModule[result.module] || { passed: 0, failed: 0, avgLoadTime: 0 };
      
      if (result.passed) {
        moduleStats.passed++;
      } else {
        moduleStats.failed++;
      }
      
      byModule[result.module] = moduleStats;
    });

    // Calculate average load times per module
    Object.keys(byModule).forEach(moduleName => {
      const moduleResults = results.filter(r => r.module === moduleName);
      const totalLoadTime = moduleResults.reduce((sum, r) => sum + r.loadTime, 0);
      byModule[moduleName].avgLoadTime = moduleResults.length > 0 ? totalLoadTime / moduleResults.length : 0;
    });

    // Get slowest routes
    const slowestRoutes = results
      .filter(r => r.passed)
      .sort((a, b) => b.loadTime - a.loadTime)
      .slice(0, 5)
      .map(r => ({ path: r.path, loadTime: r.loadTime }));

    // Get failed routes
    const failedRoutes = results
      .filter(r => !r.passed)
      .map(r => ({ path: r.path, error: r.error || 'Unknown error' }));

    return {
      byModule,
      slowestRoutes,
      failedRoutes
    };
  }

  /**
   * Get module for a route
   */
  private getModuleForRoute(path: string): string {
    const modules = routeRegistry.getModules();
    
    for (const module of modules) {
      if (Object.keys(module.routes).includes(path)) {
        return module.name;
      }
    }
    
    return 'unknown';
  }

  /**
   * Generate test report
   */
  generateReport(testSuite: RouteTestSuite): string {
    const report = [
      '# Route Test Report',
      `Generated: ${testSuite.timestamp}`,
      '',
      '## Summary',
      `- Total Routes: ${testSuite.totalTests}`,
      `- Passed: ${testSuite.passed}`,
      `- Failed: ${testSuite.failed}`,
      `- Success Rate: ${((testSuite.passed / testSuite.totalTests) * 100).toFixed(1)}%`,
      `- Average Load Time: ${testSuite.averageLoadTime.toFixed(2)}ms`,
      '',
      '## By Module',
      ...Object.entries(testSuite.summary.byModule).map(([module, stats]) => 
        `- ${module}: ${stats.passed}/${stats.passed + stats.failed} passed (${stats.avgLoadTime.toFixed(2)}ms avg)`
      ),
      '',
      '## Slowest Routes',
      ...testSuite.summary.slowestRoutes.map(route => 
        `- ${route.path}: ${route.loadTime.toFixed(2)}ms`
      ),
      ''
    ];

    if (testSuite.summary.failedRoutes.length > 0) {
      report.push(
        '## Failed Routes',
        ...testSuite.summary.failedRoutes.map(route => 
          `- ${route.path}: ${route.error}`
        ),
        ''
      );
    }

    return report.join('\n');
  }
}

// Export singleton instance
export const routeTester = RouteTester.getInstance();

// Export convenience functions
export const testAllRoutes = () => routeTester.testAllRoutes();
export const testRoute = (path: string) => routeTester.testRoute(path);
export const testModule = (moduleName: string) => routeTester.testModule(moduleName);
export const benchmarkRoutes = (iterations?: number) => routeTester.benchmarkRoutes(iterations);
