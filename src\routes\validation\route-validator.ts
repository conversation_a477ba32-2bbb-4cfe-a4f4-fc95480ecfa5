/**
 * Comprehensive route validation system
 * Ensures all routes have corresponding components and proper configuration
 */

import { routeRegistry, getAllRouteMetadata } from '../modules';
import { RouteValidationResult, RouteMetadata } from '../modules/types';

export interface ValidationReport {
  timestamp: string;
  summary: {
    totalRoutes: number;
    validRoutes: number;
    invalidRoutes: number;
    missingComponents: number;
    duplicateRoutes: number;
    orphanedMetadata: number;
  };
  details: {
    missingComponents: Array<{
      path: string;
      module: string;
      error: string;
    }>;
    duplicateRoutes: Array<{
      path: string;
      modules: string[];
    }>;
    orphanedMetadata: Array<{
      path: string;
      reason: string;
    }>;
    invalidPaths: Array<{
      path: string;
      issue: string;
    }>;
  };
  recommendations: string[];
}

/**
 * Advanced Route Validator
 */
export class RouteValidator {
  private static instance: RouteValidator;

  static getInstance(): RouteValidator {
    if (!RouteValidator.instance) {
      RouteValidator.instance = new RouteValidator();
    }
    return RouteValidator.instance;
  }

  /**
   * Validate all routes comprehensively
   */
  async validateAllRoutes(): Promise<ValidationReport> {
    const startTime = Date.now();
    const report: ValidationReport = {
      timestamp: new Date().toISOString(),
      summary: {
        totalRoutes: 0,
        validRoutes: 0,
        invalidRoutes: 0,
        missingComponents: 0,
        duplicateRoutes: 0,
        orphanedMetadata: 0
      },
      details: {
        missingComponents: [],
        duplicateRoutes: [],
        orphanedMetadata: [],
        invalidPaths: []
      },
      recommendations: []
    };

    try {
      // Get all routes and metadata
      const allRoutes = routeRegistry.getAllRoutes();
      const allMetadata = getAllRouteMetadata();
      const modules = routeRegistry.getModules();

      report.summary.totalRoutes = allRoutes.length;

      // Validate route components
      await this.validateRouteComponents(allRoutes, report);

      // Check for duplicate routes
      this.checkDuplicateRoutes(modules, report);

      // Validate route paths
      this.validateRoutePaths(allRoutes, report);

      // Check metadata consistency
      this.validateMetadata(allRoutes, allMetadata, report);

      // Generate recommendations
      this.generateRecommendations(report);

      // Calculate summary
      report.summary.validRoutes = report.summary.totalRoutes - report.summary.invalidRoutes;

      if (import.meta.env.DEV) {
        console.log(`Route validation completed in ${Date.now() - startTime}ms`);
      }

    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Route validation failed:', error);
      }
      report.recommendations.push('Route validation encountered errors. Check console for details.');
    }

    return report;
  }

  /**
   * Validate that all routes have corresponding components
   */
  private async validateRouteComponents(
    routes: string[], 
    report: ValidationReport
  ): Promise<void> {
    const componentValidationPromises = routes.map(async (path) => {
      try {
        const loader = routeRegistry.getRouteLoader(path);
        if (!loader) {
          report.details.missingComponents.push({
            path,
            module: this.getModuleForRoute(path),
            error: 'No loader function found'
          });
          return false;
        }

        // Try to load the component to verify it exists
        try {
          await loader();
          return true;
        } catch (error) {
          report.details.missingComponents.push({
            path,
            module: this.getModuleForRoute(path),
            error: error instanceof Error ? error.message : 'Component load failed'
          });
          return false;
        }
      } catch (error) {
        report.details.missingComponents.push({
          path,
          module: this.getModuleForRoute(path),
          error: error instanceof Error ? error.message : 'Unknown validation error'
        });
        return false;
      }
    });

    const results = await Promise.all(componentValidationPromises);
    report.summary.missingComponents = results.filter(result => !result).length;
    report.summary.invalidRoutes += report.summary.missingComponents;
  }

  /**
   * Check for duplicate routes across modules
   */
  private checkDuplicateRoutes(modules: Array<{ name: string; routes: Record<string, unknown> }>, report: ValidationReport): void {
    const routeModuleMap = new Map<string, string[]>();

    modules.forEach(module => {
      Object.keys(module.routes).forEach(path => {
        if (!routeModuleMap.has(path)) {
          routeModuleMap.set(path, []);
        }
        routeModuleMap.get(path)!.push(module.name);
      });
    });

    routeModuleMap.forEach((moduleList, path) => {
      if (moduleList.length > 1) {
        report.details.duplicateRoutes.push({
          path,
          modules: moduleList
        });
      }
    });

    report.summary.duplicateRoutes = report.details.duplicateRoutes.length;
    report.summary.invalidRoutes += report.summary.duplicateRoutes;
  }

  /**
   * Validate route path formats
   */
  private validateRoutePaths(routes: string[], report: ValidationReport): void {
    routes.forEach(path => {
      // Check for valid path format
      if (!path.startsWith('/') && path !== '*') {
        report.details.invalidPaths.push({
          path,
          issue: 'Path must start with / or be * for catch-all'
        });
      }

      // Check for double slashes
      if (path.includes('//')) {
        report.details.invalidPaths.push({
          path,
          issue: 'Path contains double slashes'
        });
      }

      // Check for trailing slashes (except root)
      if (path.length > 1 && path.endsWith('/')) {
        report.details.invalidPaths.push({
          path,
          issue: 'Path should not end with trailing slash'
        });
      }

      // Check for invalid characters
      const invalidChars = /[^a-zA-Z0-9\-_/*]/;
      if (invalidChars.test(path)) {
        report.details.invalidPaths.push({
          path,
          issue: 'Path contains invalid characters'
        });
      }
    });

    report.summary.invalidRoutes += report.details.invalidPaths.length;
  }

  /**
   * Validate metadata consistency
   */
  private validateMetadata(
    routes: string[], 
    metadata: Record<string, RouteMetadata>, 
    report: ValidationReport
  ): void {
    // Check for orphaned metadata (metadata without corresponding routes)
    Object.keys(metadata).forEach(path => {
      if (!routes.includes(path)) {
        report.details.orphanedMetadata.push({
          path,
          reason: 'Metadata exists but route is not registered'
        });
      }
    });

    report.summary.orphanedMetadata = report.details.orphanedMetadata.length;
  }

  /**
   * Generate recommendations based on validation results
   */
  private generateRecommendations(report: ValidationReport): void {
    if (report.summary.missingComponents > 0) {
      report.recommendations.push(
        `Fix ${report.summary.missingComponents} missing component(s) by creating the corresponding page files or updating import paths.`
      );
    }

    if (report.summary.duplicateRoutes > 0) {
      report.recommendations.push(
        `Resolve ${report.summary.duplicateRoutes} duplicate route(s) by removing redundant registrations or consolidating modules.`
      );
    }

    if (report.details.invalidPaths.length > 0) {
      report.recommendations.push(
        `Fix ${report.details.invalidPaths.length} invalid path format(s) to ensure proper routing.`
      );
    }

    if (report.summary.orphanedMetadata > 0) {
      report.recommendations.push(
        `Clean up ${report.summary.orphanedMetadata} orphaned metadata entries or register missing routes.`
      );
    }

    if (report.summary.invalidRoutes === 0) {
      report.recommendations.push('✅ All routes are valid! Consider implementing route preloading for better performance.');
    }

    // Performance recommendations
    if (report.summary.totalRoutes > 50) {
      report.recommendations.push('Consider implementing route-based code splitting for better performance with large route counts.');
    }
  }

  /**
   * Get the module name for a given route
   */
  private getModuleForRoute(path: string): string {
    const modules = routeRegistry.getModules();
    
    for (const module of modules) {
      if (Object.keys(module.routes).includes(path)) {
        return module.name;
      }
    }
    
    return 'unknown';
  }

  /**
   * Quick validation check (synchronous)
   */
  quickValidate(): RouteValidationResult {
    return routeRegistry.validateRoutes();
  }

  /**
   * Validate a specific route
   */
  async validateRoute(path: string): Promise<boolean> {
    try {
      const loader = routeRegistry.getRouteLoader(path);
      if (!loader) return false;
      
      await loader();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get validation summary
   */
  getValidationSummary(): { isValid: boolean; message: string } {
    const quickResult = this.quickValidate();
    
    if (quickResult.isValid) {
      return {
        isValid: true,
        message: `✅ All ${routeRegistry.getAllRoutes().length} routes are valid`
      };
    } else {
      return {
        isValid: false,
        message: `❌ Found ${quickResult.errors.length} errors and ${quickResult.warnings.length} warnings`
      };
    }
  }
}

// Export singleton instance
export const routeValidator = RouteValidator.getInstance();

// Export convenience functions
export const validateAllRoutes = () => routeValidator.validateAllRoutes();
export const quickValidateRoutes = () => routeValidator.quickValidate();
export const validateRoute = (path: string) => routeValidator.validateRoute(path);
export const getValidationSummary = () => routeValidator.getValidationSummary();
