import path from "path";

import react from "@vitejs/plugin-react-swc";
import { componentTagger } from "lovable-tagger";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080, // Updated port from 3000 to 8080
    headers: {
      'Cache-Control': 'no-cache',
    },
    middlewareMode: false,
    fs: {
      strict: true,
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'lucide-react',
      'date-fns',
      'clsx',
      'tailwind-merge',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-tooltip'
    ],
    exclude: ['lovable-tagger']
  },
  css: {
    devSourcemap: true,
  },
  build: {
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: mode === 'development',
    minify: mode === 'production' ? 'terser' : 'esbuild',
    cssMinify: mode === 'production',
    terserOptions: mode === 'production' ? {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
        passes: 2,
        unsafe_arrows: true,
        unsafe_methods: true,
        unsafe_proto: true,
        unsafe_regexp: true,
        unsafe_undefined: true
      },
      mangle: {
        safari10: true,
        properties: {
          regex: /^_/
        }
      },
      format: {
        comments: false
      }
    } : undefined,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500, // Reduced from 1000 to enforce smaller chunks
    rollupOptions: {
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false
      },
      output: {
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        entryFileNames: 'assets/[name]-[hash].js',
        manualChunks: (id) => {
          // Vendor chunks - more granular splitting
          if (id.includes('node_modules')) {
            // Split React ecosystem into smaller chunks
            if (id.includes('react-dom')) {
              return 'vendor-react-dom';
            }
            if (id.includes('react-router')) {
              return 'vendor-react-router';
            }
            if (id.includes('react') && !id.includes('react-dom') && !id.includes('react-router')) {
              return 'vendor-react-core';
            }
            // Split Radix UI components
            if (id.includes('@radix-ui')) {
              if (id.includes('dialog') || id.includes('popover') || id.includes('dropdown')) {
                return 'vendor-ui-overlays';
              }
              if (id.includes('accordion') || id.includes('tabs') || id.includes('collapsible')) {
                return 'vendor-ui-navigation';
              }
              if (id.includes('select') || id.includes('checkbox') || id.includes('radio')) {
                return 'vendor-ui-forms';
              }
              return 'vendor-ui-core';
            }
            if (id.includes('lucide-react')) {
              return 'vendor-icons';
            }
            if (id.includes('clsx') || id.includes('tailwind-merge') || id.includes('date-fns')) {
              return 'vendor-utils';
            }
            if (id.includes('@tanstack/react-query')) {
              return 'vendor-query';
            }
            // Other vendor libraries
            return 'vendor-misc';
          }

          // Location data chunks (these are large)
          if (id.includes('/data/locations/')) {
            return 'location-data';
          }

          // Medical condition data chunks - split by type
          if (id.includes('/data/conditions/')) {
            // Brain condition data
            if (id.includes('cerebral') || id.includes('brain') || id.includes('chiari') || id.includes('hydrocephalus')) {
              return 'brain-condition-data';
            }
            // Spine condition data
            if (id.includes('cervical') || id.includes('lumbar') || id.includes('spine') || id.includes('cauda') || id.includes('disc')) {
              return 'spine-condition-data';
            }
            // Nerve condition data
            if (id.includes('nerve') || id.includes('neuralgia') || id.includes('neuropathy') || id.includes('tunnel') || id.includes('carpal')) {
              return 'nerve-condition-data';
            }
            // Other condition data
            return 'other-condition-data';
          }

          // Split medical condition pages by type
          if (id.includes('/conditions/') && id.includes('.tsx')) {
            // Brain conditions
            if (id.includes('Cerebral') || id.includes('Brain') || id.includes('Chiari') || id.includes('Hydrocephalus')) {
              return 'brain-conditions';
            }
            // Spine conditions
            if (id.includes('Cervical') || id.includes('Lumbar') || id.includes('Spine') || id.includes('Cauda')) {
              return 'spine-conditions';
            }
            // Nerve conditions
            if (id.includes('Nerve') || id.includes('Neuralgia') || id.includes('Neuropathy') || id.includes('Tunnel')) {
              return 'nerve-conditions';
            }
            // Other conditions
            return 'other-conditions';
          }

          // Split patient resource pages by type
          if (id.includes('/patient-resources/')) {
            // Exercise-related pages
            if (id.includes('Exercise') || id.includes('exercise')) {
              return 'exercise-resources';
            }
            // Spine-related pages
            if (id.includes('Spine') || id.includes('spine') || id.includes('Cervical') || id.includes('Lumbar')) {
              return 'spine-resources';
            }
            // Brain-related pages
            if (id.includes('Brain') || id.includes('brain')) {
              return 'brain-resources';
            }
            // Condition pages
            if (id.includes('/conditions/')) {
              return 'condition-pages';
            }
            // Lifestyle modification pages
            if (id.includes('/lifestyle-modifications/')) {
              return 'lifestyle-resources';
            }
            // Assessment and dashboard pages
            if (id.includes('Assessment') || id.includes('Dashboard') || id.includes('Programme')) {
              return 'assessment-resources';
            }
            // Other patient resources
            return 'other-patient-resources';
          }

          // GP resources
          if (id.includes('/gp-resources/')) {
            return 'gp-resources';
          }

          // Expertise pages
          if (id.includes('/expertise/')) {
            return 'expertise';
          }

          // Large component libraries
          if (id.includes('/medical-conditions/') && id.includes('.tsx')) {
            // Split by condition type
            if (id.includes('cerebral') || id.includes('brain') || id.includes('chiari') || id.includes('hydrocephalus')) {
              return 'brain-condition-components';
            }
            if (id.includes('cervical') || id.includes('lumbar') || id.includes('spine') || id.includes('cauda')) {
              return 'spine-condition-components';
            }
            if (id.includes('nerve') || id.includes('neuralgia') || id.includes('neuropathy') || id.includes('tunnel')) {
              return 'nerve-condition-components';
            }
            return 'condition-components';
          }

          // Split very large individual condition files
          if (id.includes('/data/conditions/')) {
            // Large individual files get their own chunks
            if (id.includes('hydrocephalus')) return 'hydrocephalus-data';
            if (id.includes('cervicalMyelopathy')) return 'cervical-myelopathy-data';
            if (id.includes('caudaEquinaSyndrome')) return 'cauda-equina-data';
            if (id.includes('chiariMalformation')) return 'chiari-data';
            if (id.includes('hemifacialSpasm')) return 'hemifacial-spasm-data';
            if (id.includes('cerebralAVM')) return 'cerebral-avm-data';
          }
        }
      }
    }
  },
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts'],
    globals: true,
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/setupTests.ts',
        'src/main.tsx',
        'src/vite-env.d.ts',
        '**/*.d.ts',
        'dist/',
        'docs/',
        'scripts/',
        '**/*.config.*',
        'src/tests/**/*'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    include: ['src/tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
  },
}));
